"""
Quick SSL fix for PaddleOCR using your existing certifi certificate bundle.
This script configures the environment to use the certifi certificates you already have.
"""

import os
import sys

def configure_ssl_with_certifi():
    """Configure SSL using the certifi certificate bundle."""
    try:
        import certifi
        cert_path = certifi.where()

        print(f"Found certifi certificate bundle: {cert_path}")

        if not os.path.exists(cert_path):
            print(f"❌ Certificate bundle not found at: {cert_path}")
            return False

        # Set environment variables
        os.environ['REQUESTS_CA_BUNDLE'] = cert_path
        os.environ['CURL_CA_BUNDLE'] = cert_path
        os.environ['SSL_CERT_FILE'] = cert_path

        print("✅ SSL environment variables configured:")
        print(f"   REQUESTS_CA_BUNDLE = {cert_path}")
        print(f"   CURL_CA_BUNDLE = {cert_path}")
        print(f"   SSL_CERT_FILE = {cert_path}")

        return True

    except ImportError:
        print("❌ Certifi package not found. Install with: pip install certifi")
        return False
    except Exception as e:
        print(f"❌ Error configuring SSL: {e}")
        return False

def configure_ssl_context():
    """Configure SSL context at the Python level."""
    import ssl
    import urllib3

    try:
        # Get the certifi certificate path
        import certifi
        cert_path = certifi.where()

        # Create SSL context with certifi certificates
        context = ssl.create_default_context(cafile=cert_path)

        # Monkey patch the default HTTPS context
        ssl._create_default_https_context = lambda: context

        # Also disable urllib3 warnings
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        print(f"✅ SSL context configured with certifi: {cert_path}")
        return True

    except Exception as e:
        print(f"❌ Failed to configure SSL context: {e}")

        # Fallback: disable SSL verification entirely
        try:
            ssl._create_default_https_context = ssl._create_unverified_context
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            print("⚠️  SSL verification disabled as fallback")
            return True
        except Exception as e2:
            print(f"❌ SSL fallback also failed: {e2}")
            return False

def test_paddleocr_connection():
    """Test if PaddleOCR can now download models."""
    print("\n🧪 Testing PaddleOCR model download...")

    # Configure SSL context before importing PaddleOCR
    if not configure_ssl_context():
        print("❌ SSL context configuration failed")
        return False

    try:
        from paddleocr import PaddleOCR

        # Try to initialize PaddleOCR (this will trigger model download)
        print("Initializing PaddleOCR (this may take a moment for first-time setup)...")

        # Try different initialization approaches for different PaddleOCR versions
        try:
            # Try with minimal parameters first
            ocr = PaddleOCR(lang='en')
        except Exception as e1:
            try:
                # Try with use_angle_cls parameter
                ocr = PaddleOCR(lang='en', use_angle_cls=True)
            except Exception as e2:
                try:
                    # Try with newer parameter name
                    ocr = PaddleOCR(lang='en', use_textline_orientation=True)
                except Exception as e3:
                    raise Exception(f"All initialization attempts failed: {e1}, {e2}, {e3}")

        print("✅ PaddleOCR initialized successfully!")
        print("✅ SSL certificate configuration is working!")
        return True

    except Exception as e:
        print(f"❌ PaddleOCR initialization failed: {e}")
        return False

def main():
    print("="*60)
    print("PADDLEOCR SSL CERTIFICATE FIX")
    print("="*60)
    print("This script configures SSL to use your existing certifi certificates.")
    print()

    # Configure SSL
    if not configure_ssl_with_certifi():
        print("\n❌ SSL configuration failed!")
        sys.exit(1)

    # Ask if user wants to test PaddleOCR
    print("\n" + "="*60)
    test_choice = input("Do you want to test PaddleOCR initialization now? (y/n): ").strip().lower()

    if test_choice == 'y':
        success = test_paddleocr_connection()

        if success:
            print("\n🎉 SUCCESS! PaddleOCR is now working with SSL certificates.")
            print("\nYou can now run your redaction script:")
            print("python redact_multi_ocr.py input.pdf output.pdf targets.json")
        else:
            print("\n⚠️  PaddleOCR test failed, but SSL is configured.")
            print("The redaction script will still work with Tesseract + EasyOCR.")
    else:
        print("\n✅ SSL configured. You can now run your redaction script.")

    print("\n" + "="*60)
    print("ENVIRONMENT VARIABLES SET FOR THIS SESSION:")
    print("="*60)
    print(f"REQUESTS_CA_BUNDLE = {os.environ.get('REQUESTS_CA_BUNDLE', 'Not set')}")
    print(f"CURL_CA_BUNDLE = {os.environ.get('CURL_CA_BUNDLE', 'Not set')}")
    print(f"SSL_CERT_FILE = {os.environ.get('SSL_CERT_FILE', 'Not set')}")

    print("\nNote: These settings are temporary for this command prompt session.")
    print("To make them permanent, add them to your system environment variables.")

if __name__ == "__main__":
    main()
