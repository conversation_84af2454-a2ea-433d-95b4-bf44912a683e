"""
Trace exactly which certificate bundle PaddleOCR is using by monkey-patching SSL calls.
This will show us the exact certificate path being used during the download.
"""

import os
import sys
import ssl
import urllib3
import requests
import urllib.request
import certifi

# Store original functions
original_create_default_context = ssl.create_default_context
original_urlopen = urllib.request.urlopen
original_request = requests.request

def trace_ssl_context(*args, **kwargs):
    """Trace SSL context creation."""
    print(f"🔍 SSL Context Created:")
    print(f"  Args: {args}")
    print(f"  Kwargs: {kwargs}")
    
    # Check what certificate file is being used
    if 'cafile' in kwargs:
        print(f"  📋 Using cafile: {kwargs['cafile']}")
    else:
        print(f"  📋 No cafile specified, using default")
        
    # Check environment variables
    env_vars = ['REQUESTS_CA_BUNDLE', 'CURL_CA_BUNDLE', 'SSL_CERT_FILE']
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            print(f"  🌍 {var}: {value}")
    
    # Get default verify paths
    verify_paths = ssl.get_default_verify_paths()
    print(f"  🔗 Default cafile: {verify_paths.cafile}")
    print(f"  🔗 Default capath: {verify_paths.capath}")
    print(f"  🔗 OpenSSL cafile: {verify_paths.openssl_cafile}")
    
    # Call original function
    context = original_create_default_context(*args, **kwargs)
    
    # Try to get the CA certs being used
    try:
        ca_certs = context.get_ca_certs()
        print(f"  📊 Loaded {len(ca_certs)} CA certificates")
    except:
        print(f"  📊 Could not get CA certificate count")
    
    return context

def trace_urlopen(url, *args, **kwargs):
    """Trace urllib.request.urlopen calls."""
    print(f"🌐 urllib.request.urlopen called:")
    print(f"  URL: {url}")
    
    if 'context' in kwargs:
        print(f"  🔒 Custom SSL context provided")
    else:
        print(f"  🔒 Using default SSL context")
    
    # Call original function
    try:
        return original_urlopen(url, *args, **kwargs)
    except Exception as e:
        print(f"  ❌ urllib.request.urlopen failed: {e}")
        raise

def trace_requests(*args, **kwargs):
    """Trace requests library calls."""
    if args:
        method = args[0]
        url = args[1] if len(args) > 1 else kwargs.get('url', 'Unknown')
        print(f"🌐 requests.{method} called:")
        print(f"  URL: {url}")
    
    # Check if verify parameter is set
    verify = kwargs.get('verify', True)
    print(f"  🔒 Verify: {verify}")
    
    if isinstance(verify, str):
        print(f"  📋 Custom cert bundle: {verify}")
    elif verify is True:
        print(f"  📋 Using default cert bundle")
        # Check what requests would use by default
        try:
            import requests.certs
            default_bundle = requests.certs.where()
            print(f"  📋 Requests default bundle: {default_bundle}")
        except:
            pass
    
    # Call original function
    try:
        return original_request(*args, **kwargs)
    except Exception as e:
        print(f"  ❌ requests failed: {e}")
        raise

def patch_ssl_functions():
    """Monkey patch SSL and HTTP functions to trace usage."""
    print("🔧 Patching SSL and HTTP functions for tracing...")
    
    # Patch SSL context creation
    ssl.create_default_context = trace_ssl_context
    
    # Patch urllib
    urllib.request.urlopen = trace_urlopen
    
    # Patch requests
    requests.request = trace_requests
    
    # Also patch urllib3 if needed
    try:
        import urllib3.util.ssl_
        original_create_urllib3_context = urllib3.util.ssl_.create_urllib3_context
        
        def trace_urllib3_context(*args, **kwargs):
            print(f"🔍 urllib3 SSL Context Created:")
            print(f"  Args: {args}")
            print(f"  Kwargs: {kwargs}")
            return original_create_urllib3_context(*args, **kwargs)
        
        urllib3.util.ssl_.create_urllib3_context = trace_urllib3_context
    except:
        pass

def show_current_ssl_config():
    """Show current SSL configuration."""
    print("="*70)
    print("CURRENT SSL CONFIGURATION")
    print("="*70)
    
    # Environment variables
    print("Environment Variables:")
    ssl_vars = ['REQUESTS_CA_BUNDLE', 'CURL_CA_BUNDLE', 'SSL_CERT_FILE', 'PYTHONHTTPSVERIFY']
    for var in ssl_vars:
        value = os.environ.get(var, 'Not set')
        print(f"  {var}: {value}")
    
    # Default verify paths
    print(f"\nSSL Default Verify Paths:")
    verify_paths = ssl.get_default_verify_paths()
    print(f"  cafile: {verify_paths.cafile}")
    print(f"  capath: {verify_paths.capath}")
    print(f"  openssl_cafile: {verify_paths.openssl_cafile}")
    print(f"  openssl_capath: {verify_paths.openssl_capath}")
    
    # Certifi
    try:
        certifi_path = certifi.where()
        print(f"\nCertifi bundle: {certifi_path}")
        print(f"Certifi exists: {os.path.exists(certifi_path)}")
    except:
        print(f"\nCertifi: Not available")
    
    # Requests default
    try:
        import requests.certs
        requests_bundle = requests.certs.where()
        print(f"\nRequests default bundle: {requests_bundle}")
        print(f"Requests bundle exists: {os.path.exists(requests_bundle)}")
    except:
        print(f"\nRequests: Cannot determine default bundle")

def test_paddleocr_with_tracing():
    """Test PaddleOCR initialization with SSL tracing enabled."""
    print("\n" + "="*70)
    print("TESTING PADDLEOCR WITH SSL TRACING")
    print("="*70)
    
    # Patch functions before importing PaddleOCR
    patch_ssl_functions()
    
    try:
        print("Importing PaddleOCR...")
        from paddleocr import PaddleOCR
        
        print("\nInitializing PaddleOCR (this will trigger the SSL calls)...")
        ocr = PaddleOCR(lang='en')
        
        print("✅ PaddleOCR initialized successfully!")
        
    except Exception as e:
        print(f"❌ PaddleOCR initialization failed: {e}")
        print("\nThe trace above shows exactly which certificate bundle was attempted.")

def main():
    print("="*70)
    print("TRACE PADDLEOCR SSL CERTIFICATE USAGE")
    print("="*70)
    print("This script traces exactly which certificate bundle PaddleOCR uses.")
    print()
    
    # Show current configuration
    show_current_ssl_config()
    
    # Test with tracing
    test_paddleocr_with_tracing()
    
    print("\n" + "="*70)
    print("ANALYSIS COMPLETE")
    print("="*70)
    print("The trace above shows:")
    print("1. Which SSL context creation method was called")
    print("2. What certificate bundle path was used")
    print("3. Which HTTP library made the actual request")
    print("4. The exact point of SSL failure")
    print("\nUse this information to add the certificate to the correct bundle!")

if __name__ == "__main__":
    main()
