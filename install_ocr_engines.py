"""
Installation script for additional OCR engines to improve PII redaction accuracy.
Run this script to install EasyOCR and PaddleOCR for better results on scanned documents.
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{description}...")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("⚠️  Warning: Python 3.7+ is recommended for best compatibility")
        return False
    return True

def install_easyocr():
    """Install EasyOCR."""
    print("\n" + "="*50)
    print("Installing EasyOCR")
    print("="*50)
    
    # EasyOCR installation
    commands = [
        ("pip install easyocr", "Installing EasyOCR"),
    ]
    
    success = True
    for command, description in commands:
        if not run_command(command, description):
            success = False
    
    if success:
        print("✓ EasyOCR installation completed!")
        print("  - Supports 80+ languages")
        print("  - Good for handwritten text")
        print("  - Works well with various image qualities")
    else:
        print("✗ EasyOCR installation failed")
    
    return success

def install_paddleocr():
    """Install PaddleOCR."""
    print("\n" + "="*50)
    print("Installing PaddleOCR")
    print("="*50)
    
    # PaddleOCR installation
    commands = [
        ("pip install paddlepaddle", "Installing PaddlePaddle"),
        ("pip install paddleocr", "Installing PaddleOCR"),
    ]
    
    success = True
    for command, description in commands:
        if not run_command(command, description):
            success = False
    
    if success:
        print("✓ PaddleOCR installation completed!")
        print("  - High accuracy OCR engine")
        print("  - Good for complex layouts")
        print("  - Supports text detection and recognition")
    else:
        print("✗ PaddleOCR installation failed")
    
    return success

def test_installations():
    """Test if the OCR engines can be imported."""
    print("\n" + "="*50)
    print("Testing OCR Engine Installations")
    print("="*50)
    
    # Test EasyOCR
    try:
        import easyocr
        print("✓ EasyOCR: Successfully imported")
        easyocr_available = True
    except ImportError as e:
        print(f"✗ EasyOCR: Import failed - {e}")
        easyocr_available = False
    
    # Test PaddleOCR
    try:
        from paddleocr import PaddleOCR
        print("✓ PaddleOCR: Successfully imported")
        paddleocr_available = True
    except ImportError as e:
        print(f"✗ PaddleOCR: Import failed - {e}")
        paddleocr_available = False
    
    # Test Tesseract
    try:
        import pytesseract
        print("✓ Tesseract (pytesseract): Successfully imported")
        tesseract_available = True
    except ImportError as e:
        print(f"✗ Tesseract (pytesseract): Import failed - {e}")
        tesseract_available = False
    
    return easyocr_available, paddleocr_available, tesseract_available

def main():
    print("="*60)
    print("OCR ENGINES INSTALLATION SCRIPT")
    print("="*60)
    print("This script will install additional OCR engines to improve")
    print("PII redaction accuracy on scanned documents and images.")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        response = input("\nContinue anyway? (y/n): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    # Ask user what to install
    print("\nAvailable OCR engines to install:")
    print("1. EasyOCR - Good for general text recognition")
    print("2. PaddleOCR - High accuracy, good for complex layouts")
    print("3. Both (recommended)")
    print("4. Skip installation and test current setup")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    easyocr_success = True
    paddleocr_success = True
    
    if choice == "1":
        easyocr_success = install_easyocr()
    elif choice == "2":
        paddleocr_success = install_paddleocr()
    elif choice == "3":
        easyocr_success = install_easyocr()
        paddleocr_success = install_paddleocr()
    elif choice == "4":
        print("\nSkipping installation...")
    else:
        print("Invalid choice. Exiting.")
        sys.exit(1)
    
    # Test installations
    easyocr_available, paddleocr_available, tesseract_available = test_installations()
    
    # Summary
    print("\n" + "="*60)
    print("INSTALLATION SUMMARY")
    print("="*60)
    
    available_engines = []
    if tesseract_available:
        available_engines.append("Tesseract")
    if easyocr_available:
        available_engines.append("EasyOCR")
    if paddleocr_available:
        available_engines.append("PaddleOCR")
    
    print(f"Available OCR engines: {', '.join(available_engines) if available_engines else 'None'}")
    print(f"Total engines available: {len(available_engines)}")
    
    if len(available_engines) >= 2:
        print("\n🎉 Great! Multiple OCR engines are available.")
        print("   This will significantly improve redaction accuracy on scanned documents.")
    elif len(available_engines) == 1:
        print("\n⚠️  Only one OCR engine is available.")
        print("   Consider installing additional engines for better accuracy.")
    else:
        print("\n❌ No OCR engines are available.")
        print("   Please install at least Tesseract and pytesseract.")
    
    print("\nNext steps:")
    print("1. Use 'redact_enhanced_ocr.py' for improved single-engine processing")
    print("2. Use 'redact_multi_ocr.py' for multi-engine processing (best results)")
    print("3. Test with your scanned documents")
    
    print("\nExample usage:")
    print("python redact_multi_ocr.py input.pdf output.pdf redaction_targets.json")

if __name__ == "__main__":
    main()
