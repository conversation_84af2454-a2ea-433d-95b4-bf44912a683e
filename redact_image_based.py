import fitz  # PyMuPDF
import json
import sys
from PIL import Image, ImageDraw, ImageEnhance, ImageFilter
import pytesseract
import io
from difflib import SequenceMatcher

# Configure pytesseract to use the Tesseract installation
pytesseract.pytesseract.tesseract_cmd = r'C:\softies\tesseract\tesseract.exe'

def find_and_redact_text_on_image(image, text_to_find):
    """
    Uses OCR to find text on an image and draws black rectangles over matches.
    Returns the modified image and number of matches found.
    """
    print(f"DEBUG: Searching for text: '{text_to_find}'")

    # Create a copy of the image to work with
    img_copy = image.copy()

    # Try multiple OCR configurations for better accuracy
    ocr_configs = [
        r'--oem 3 --psm 6',  # Default
        r'--oem 3 --psm 8',  # Single word
        r'--oem 3 --psm 7',  # Single text line
        r'--oem 3 --psm 13', # Raw line
    ]

    all_ocr_results = []

    for config in ocr_configs:
        try:
            # Enhance image for better OCR
            img_enhanced = img_copy.convert('L')  # Convert to grayscale

            # Increase contrast slightly
            enhancer = ImageEnhance.Contrast(img_enhanced)
            img_enhanced = enhancer.enhance(1.5)

            ocr_data = pytesseract.image_to_data(img_enhanced, output_type=pytesseract.Output.DICT, config=config)
            all_ocr_results.append(ocr_data)
        except:
            continue

    # Use the first successful OCR result
    if not all_ocr_results:
        print("DEBUG: All OCR attempts failed")
        return img_copy, 0

    ocr_data = all_ocr_results[0]  # Use first result for now

    # Debug: Show all detected text
    detected_texts = []
    for i in range(len(ocr_data['text'])):
        text = ocr_data['text'][i].strip()
        if text and len(text) > 0:
            confidence = ocr_data['conf'][i]
            detected_texts.append(f"'{text}' (conf: {confidence})")

    print(f"DEBUG: OCR detected {len(detected_texts)} text elements:")
    for text in detected_texts[:15]:  # Show first 15 to see more results
        print(f"  {text}")
    if len(detected_texts) > 15:
        print(f"  ... and {len(detected_texts) - 15} more")

    matches_found = 0
    draw = ImageDraw.Draw(img_copy)

    # Loop through all detected text boxes
    for i in range(len(ocr_data['text'])):
        detected_text = ocr_data['text'][i].strip()
        if not detected_text:
            continue

        match_found = False
        match_type = ""
        
        # Method 1: Exact substring match
        if text_to_find.lower() in detected_text.lower():
            match_found = True
            match_type = "exact substring"
        
        # Method 2: Fuzzy matching for OCR errors (for longer strings)
        elif len(text_to_find) > 3:
            similarity = SequenceMatcher(None, text_to_find.lower(), detected_text.lower()).ratio()
            if similarity > 0.8:  # 80% similarity threshold
                match_found = True
                match_type = f"fuzzy match ({similarity:.2f})"
        
        # Method 3: Check if detected text contains most characters of target (for numbers/IDs)
        elif len(text_to_find) > 5 and text_to_find.isdigit():
            # For numeric strings, check character overlap
            common_chars = set(text_to_find.lower()) & set(detected_text.lower())
            if len(common_chars) >= len(text_to_find) * 0.7:  # 70% character overlap
                match_found = True
                match_type = "character overlap"

        if match_found:
            matches_found += 1
            print(f"DEBUG: MATCH FOUND! Looking for '{text_to_find}' found in '{detected_text}' ({match_type})")

            # Get the coordinates and dimensions of the detected text
            x, y, w, h = ocr_data['left'][i], ocr_data['top'][i], ocr_data['width'][i], ocr_data['height'][i]
            print(f"DEBUG: OCR coordinates: x={x}, y={y}, w={w}, h={h}")

            # Add some padding around the text
            padding = 5
            x1 = max(0, x - padding)
            y1 = max(0, y - padding)
            x2 = min(img_copy.width, x + w + padding)
            y2 = min(img_copy.height, y + h + padding)

            # Draw black rectangle over the text
            draw.rectangle([x1, y1, x2, y2], fill='black')
            print(f"DEBUG: Black rectangle drawn at ({x1}, {y1}, {x2}, {y2})")

    if matches_found == 0:
        print(f"DEBUG: NO MATCHES found for '{text_to_find}'")
    else:
        print(f"DEBUG: Total matches found: {matches_found}")

    return img_copy, matches_found

def redact_image_area(image, bbox):
    """
    Draws a black rectangle over the specified area on the image.
    bbox should be [x1, y1, x2, y2]
    """
    img_copy = image.copy()
    draw = ImageDraw.Draw(img_copy)

    x1, y1, x2, y2 = bbox
    # Ensure coordinates are within image bounds
    x1 = max(0, min(x1, img_copy.width))
    y1 = max(0, min(y1, img_copy.height))
    x2 = max(0, min(x2, img_copy.width))
    y2 = max(0, min(y2, img_copy.height))

    draw.rectangle([x1, y1, x2, y2], fill='black')
    print(f"DEBUG: Image area redacted at ({x1}, {y1}, {x2}, {y2})")

    return img_copy

def redact_document_image_based(input_pdf_path, output_pdf_path, redaction_targets):
    """
    Image-based redaction: Convert PDF to images, redact on images, convert back to PDF.
    """
    doc = fitz.open(input_pdf_path)
    redacted_images = []

    # Process each page
    for page_num in range(len(doc)):
        page = doc[page_num]
        print(f"\nProcessing page {page_num + 1}...")

        # Convert page to high-resolution image
        pix = page.get_pixmap(dpi=300)
        img_data = pix.tobytes("png")
        image = Image.open(io.BytesIO(img_data))
        print(f"DEBUG: Page {page_num + 1} image size: {image.size}")

        # Apply redactions for this page
        current_image = image
        page_targets = [t for t in redaction_targets if t.get("page", 1) == page_num + 1]

        for target in page_targets:
            if target["type"] == "TEXT_EXACT":
                # For TEXT_EXACT, we'll use OCR to find the text
                current_image, matches = find_and_redact_text_on_image(current_image, target["content"])

            elif target["type"] == "IMAGE_AREA":
                # For IMAGE_AREA, redact the specified coordinates
                if "bbox" in target:
                    current_image = redact_image_area(current_image, target["bbox"])
                elif "coordinates" in target:
                    current_image = redact_image_area(current_image, target["coordinates"])
                else:
                    print(f"Warning: IMAGE_AREA target on page {target['page']} is missing coordinates.")

            elif target["type"] == "TEXT_IN_IMAGE":
                # Use OCR to find and redact text
                current_image, matches = find_and_redact_text_on_image(current_image, target["content"])

        redacted_images.append(current_image)

    doc.close()

    # Convert images back to PDF
    if redacted_images:
        # Convert all images to RGB mode for PDF creation
        rgb_images = []
        for img in redacted_images:
            if img.mode != 'RGB':
                img = img.convert('RGB')
            rgb_images.append(img)

        # Save as PDF
        rgb_images[0].save(output_pdf_path, save_all=True, append_images=rgb_images[1:], format='PDF')
        print(f"\nRedacted PDF saved to: {output_pdf_path}")
    else:
        print("No images to save!")

def main():
    if len(sys.argv) != 4:
        print("Usage: python redact_image_based.py <input_pdf> <output_pdf> <redaction_json>")
        sys.exit(1)

    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    redaction_json = sys.argv[3]

    try:
        with open(redaction_json, "r") as f:
            redaction_data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"Error reading or parsing {redaction_json}: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"Error: {redaction_json} not found.")
        sys.exit(1)

    redact_document_image_based(input_pdf, output_pdf, redaction_data)
    print(f"Redaction complete. Output saved to {output_pdf}")
if __name__ == "__main__":
    main()
