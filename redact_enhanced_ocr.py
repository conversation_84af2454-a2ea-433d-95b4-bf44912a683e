import fitz  # PyMuPDF
import json
import sys
import os
from PIL import Image, ImageDraw, ImageEnhance, ImageFilter
import pytesseract
import io
import cv2
import numpy as np
from difflib import SequenceMatcher
import re
from typing import List, Dict, Tuple, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configure pytesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\softies\tesseract\tesseract.exe'

class EnhancedOCRRedactor:
    def __init__(self):
        self.dpi = 600  # Higher DPI for better OCR
        self.confidence_threshold = 20  # Lower threshold to catch more text

    def preprocess_image_advanced(self, image: Image.Image) -> List[Image.Image]:
        """
        Apply multiple preprocessing techniques to improve OCR accuracy.
        Returns a list of processed images to try.
        """
        processed_images = []

        # Convert to numpy array for OpenCV processing
        img_array = np.array(image)
        if len(img_array.shape) == 3:
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_array

        # Method 1: OTSU thresholding with Gaussian blur
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        _, thresh1 = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_images.append(Image.fromarray(thresh1))

        # Method 2: Adaptive thresholding
        adaptive_thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        processed_images.append(Image.fromarray(adaptive_thresh))

        # Method 3: Morphological operations to clean up text
        kernel = np.ones((2, 2), np.uint8)
        morph = cv2.morphologyEx(thresh1, cv2.MORPH_CLOSE, kernel)
        processed_images.append(Image.fromarray(morph))

        # Method 4: Contrast enhancement + sharpening
        enhanced = cv2.convertScaleAbs(gray, alpha=1.5, beta=30)
        kernel_sharp = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel_sharp)
        _, thresh_sharp = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_images.append(Image.fromarray(thresh_sharp))

        # Method 5: Dilation to connect broken characters
        dilated = cv2.dilate(thresh1, kernel, iterations=1)
        processed_images.append(Image.fromarray(dilated))

        return processed_images

    def extract_text_with_multiple_methods(self, page: fitz.Page) -> List[Dict]:
        """
        Extract text using multiple OCR configurations and preprocessing methods.
        """
        logger.info("Extracting text with enhanced OCR methods...")

        # Convert page to high-resolution image
        pix = page.get_pixmap(dpi=self.dpi)
        img_data = pix.tobytes("png")
        original_image = Image.open(io.BytesIO(img_data))

        # Get multiple preprocessed versions
        processed_images = self.preprocess_image_advanced(original_image)

        # Multiple OCR configurations
        ocr_configs = [
            r'--oem 3 --psm 6',   # Default
            r'--oem 3 --psm 8',   # Single word
            r'--oem 3 --psm 7',   # Single text line
            r'--oem 3 --psm 13',  # Raw line
            r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz /-:.,()#@',
            r'--oem 1 --psm 6',   # LSTM engine
            r'--oem 3 --psm 11',  # Sparse text
            r'--oem 3 --psm 12',  # Sparse text with OSD
        ]

        all_text_elements = []

        # Try each combination of preprocessing and OCR config
        for i, processed_img in enumerate(processed_images):
            for j, config in enumerate(ocr_configs):
                try:
                    ocr_data = pytesseract.image_to_data(
                        processed_img,
                        output_type=pytesseract.Output.DICT,
                        config=config
                    )

                    # Extract text elements
                    for k in range(len(ocr_data['text'])):
                        detected_text = ocr_data['text'][k].strip()
                        if not detected_text or len(detected_text) < 1:
                            continue

                        confidence = ocr_data['conf'][k]
                        if confidence < self.confidence_threshold:
                            continue

                        text_element = {
                            'text': detected_text,
                            'confidence': confidence,
                            'coordinates': (
                                ocr_data['left'][k],
                                ocr_data['top'][k],
                                ocr_data['width'][k],
                                ocr_data['height'][k]
                            ),
                            'method': f"preprocess_{i}_config_{j}",
                            'original_image_size': original_image.size
                        }
                        all_text_elements.append(text_element)

                except Exception as e:
                    logger.warning(f"OCR failed for method {i}, config {j}: {e}")
                    continue

        # Remove duplicates based on text and approximate coordinates
        unique_elements = self._remove_duplicate_detections(all_text_elements)

        logger.info(f"Extracted {len(unique_elements)} unique text elements")
        return unique_elements, original_image

    def _remove_duplicate_detections(self, text_elements: List[Dict]) -> List[Dict]:
        """
        Remove duplicate text detections that are likely the same text.
        """
        unique_elements = []

        for element in text_elements:
            is_duplicate = False

            for existing in unique_elements:
                # Check if text is similar and coordinates are close
                text_similarity = SequenceMatcher(None,
                    element['text'].lower(),
                    existing['text'].lower()
                ).ratio()

                # Check coordinate proximity (within 20 pixels)
                coord_diff = abs(element['coordinates'][0] - existing['coordinates'][0]) + \
                           abs(element['coordinates'][1] - existing['coordinates'][1])

                if text_similarity > 0.8 and coord_diff < 20:
                    # Keep the one with higher confidence
                    if element['confidence'] > existing['confidence']:
                        unique_elements.remove(existing)
                        unique_elements.append(element)
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_elements.append(element)

        return unique_elements

    def find_text_matches(self, text_elements: List[Dict], target_text: str) -> List[Dict]:
        """
        Find matches for target text using multiple matching strategies.
        """
        logger.info(f"Searching for: '{target_text}'")
        matches = []

        for element in text_elements:
            detected_text = element['text']
            match_score = 0
            match_type = ""

            # Strategy 1: Exact match (case insensitive)
            if target_text.lower() == detected_text.lower():
                match_score = 1.0
                match_type = "exact_match"

            # Strategy 2: Substring match
            elif target_text.lower() in detected_text.lower() or detected_text.lower() in target_text.lower():
                match_score = 0.95
                match_type = "substring_match"

            # Strategy 3: Fuzzy matching for longer strings
            elif len(target_text) > 3:
                similarity = SequenceMatcher(None, target_text.lower(), detected_text.lower()).ratio()
                if similarity > 0.7:
                    match_score = similarity
                    match_type = f"fuzzy_match_{similarity:.2f}"

            # Strategy 4: Number/ID matching with character overlap
            elif any(c.isdigit() for c in target_text) and len(target_text) > 3:
                # Remove common separators for comparison
                target_clean = re.sub(r'[^\w]', '', target_text.lower())
                detected_clean = re.sub(r'[^\w]', '', detected_text.lower())

                if target_clean in detected_clean or detected_clean in target_clean:
                    match_score = 0.9
                    match_type = "number_match"
                else:
                    common_chars = set(target_clean) & set(detected_clean)
                    if len(common_chars) > 0:
                        overlap_ratio = len(common_chars) / max(len(set(target_clean)), len(set(detected_clean)))
                        if overlap_ratio > 0.6:
                            match_score = overlap_ratio
                            match_type = f"char_overlap_{overlap_ratio:.2f}"

            # Strategy 5: Word-based matching for addresses/names
            elif len(target_text) > 5:
                target_words = set(re.findall(r'\w+', target_text.lower()))
                detected_words = set(re.findall(r'\w+', detected_text.lower()))

                if target_words and detected_words:
                    common_words = target_words & detected_words
                    if common_words:
                        word_ratio = len(common_words) / len(target_words)
                        if word_ratio > 0.5:
                            match_score = word_ratio
                            match_type = f"word_match_{word_ratio:.2f}"

            if match_score > 0:
                match_info = {
                    'target_text': target_text,
                    'detected_text': detected_text,
                    'match_score': match_score,
                    'match_type': match_type,
                    'confidence': element['confidence'],
                    'coordinates': element['coordinates'],
                    'method': element['method'],
                    'original_image_size': element['original_image_size']
                }
                matches.append(match_info)

        # Sort by match score and confidence
        matches.sort(key=lambda x: (x['match_score'], x['confidence']), reverse=True)

        logger.info(f"Found {len(matches)} matches for '{target_text}'")
        for i, match in enumerate(matches[:3]):  # Show top 3
            logger.info(f"  {i+1}. '{match['detected_text']}' - {match['match_type']} "
                       f"(conf: {match['confidence']}, method: {match['method']})")

        return matches

    def apply_redactions_with_padding(self, page: fitz.Page, matches: List[Dict]) -> int:
        """
        Apply redaction annotations with intelligent padding.
        """
        redactions_applied = 0

        # Group nearby matches to avoid over-redaction
        filtered_matches = self._filter_overlapping_matches(matches)

        for match in filtered_matches[:3]:  # Apply top 3 matches max
            x, y, w, h = match['coordinates']
            img_width, img_height = match['original_image_size']

            # Convert image coordinates to PDF coordinates
            pdf_width, pdf_height = page.rect.width, page.rect.height

            # Scale coordinates
            scale_x = pdf_width / img_width
            scale_y = pdf_height / img_height

            x1 = x * scale_x
            y1 = y * scale_y
            x2 = (x + w) * scale_x
            y2 = (y + h) * scale_y

            # Add intelligent padding based on text size
            padding_x = max(3, w * scale_x * 0.1)  # 10% of width or 3px minimum
            padding_y = max(2, h * scale_y * 0.1)  # 10% of height or 2px minimum

            x1 = max(0, x1 - padding_x)
            y1 = max(0, y1 - padding_y)
            x2 = min(pdf_width, x2 + padding_x)
            y2 = min(pdf_height, y2 + padding_y)

            # Create redaction annotation
            redact_rect = fitz.Rect(x1, y1, x2, y2)
            page.add_redact_annot(redact_rect)

            logger.info(f"Applied redaction for '{match['detected_text']}' "
                       f"at ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f}) "
                       f"using {match['method']}")
            redactions_applied += 1

        return redactions_applied

    def _filter_overlapping_matches(self, matches: List[Dict]) -> List[Dict]:
        """
        Filter out overlapping matches to avoid over-redaction.
        """
        if not matches:
            return matches

        filtered = [matches[0]]  # Always include the best match

        for match in matches[1:]:
            is_overlapping = False

            for existing in filtered:
                # Check if rectangles overlap significantly
                x1, y1, w1, h1 = match['coordinates']
                x2, y2, w2, h2 = existing['coordinates']

                # Calculate overlap
                overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
                overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
                overlap_area = overlap_x * overlap_y

                area1 = w1 * h1
                area2 = w2 * h2

                # If overlap is more than 30% of either rectangle, consider it overlapping
                if overlap_area > 0.3 * min(area1, area2):
                    is_overlapping = True
                    break

            if not is_overlapping:
                filtered.append(match)

        return filtered

def main():
    if len(sys.argv) != 4:
        print("Usage: python redact_enhanced_ocr.py <input_pdf> <output_pdf> <redaction_json>")
        sys.exit(1)

    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    redaction_json = sys.argv[3]

    # Validate input files
    if not os.path.exists(input_pdf):
        logger.error(f"Input PDF not found: {input_pdf}")
        sys.exit(1)

    if not os.path.exists(redaction_json):
        logger.error(f"Redaction JSON not found: {redaction_json}")
        sys.exit(1)

    try:
        with open(redaction_json, "r") as f:
            redaction_data = json.load(f)
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing {redaction_json}: {e}")
        sys.exit(1)

    # Initialize the enhanced redactor
    redactor = EnhancedOCRRedactor()

    try:
        doc = fitz.open(input_pdf)
    except Exception as e:
        logger.error(f"Error opening PDF: {e}")
        sys.exit(1)

    # Group targets by page for efficiency
    targets_by_page = {}
    for target in redaction_data:
        page_num = target.get("page", 1)
        if page_num not in targets_by_page:
            targets_by_page[page_num] = []
        targets_by_page[page_num].append(target)

    total_redactions = 0

    # Process each page
    for page_num in range(len(doc)):
        page = doc[page_num]
        page_targets = targets_by_page.get(page_num + 1, [])

        if not page_targets:
            continue

        logger.info(f"\nProcessing page {page_num + 1} with {len(page_targets)} targets...")

        # Extract all text from page once (cache OCR results)
        text_elements, original_image = redactor.extract_text_with_multiple_methods(page)

        # Process all targets for this page using cached OCR results
        for target in page_targets:
            if target["type"] in ["TEXT_EXACT", "TEXT_IN_IMAGE"]:
                content = target["content"]

                # Search in cached results
                matches = redactor.find_text_matches(text_elements, content)

                # Apply redactions
                if matches:
                    redactions_applied = redactor.apply_redactions_with_padding(page, matches)
                    total_redactions += redactions_applied
                else:
                    logger.warning(f"No matches found for '{content}'")

            elif target["type"] == "IMAGE_AREA":
                coords = target.get("bbox") or target.get("coordinates")
                if coords:
                    x1, y1, x2, y2 = coords
                    rect = fitz.Rect(x1, y1, x2, y2)
                    page.add_redact_annot(rect)
                    logger.info(f"Image area redacted at ({x1}, {y1}, {x2}, {y2})")
                    total_redactions += 1

    # Apply all redactions
    logger.info("Applying all redactions...")
    for page in doc:
        page.apply_redactions()

    # Save the document
    try:
        doc.save(output_pdf, garbage=4, deflate=True, clean=True)
        doc.close()
        logger.info(f"\nEnhanced OCR redaction complete!")
        logger.info(f"Total redactions applied: {total_redactions}")
        logger.info(f"Output saved to: {output_pdf}")
    except Exception as e:
        logger.error(f"Error saving output PDF: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
