"""
Multi-OCR Engine PII Redaction Script
Uses multiple OCR engines for maximum accuracy on scanned documents.

Requirements:
- pip install easyocr paddlepaddle paddleocr
- Tesseract (already configured)
"""

import fitz  # PyMuPDF
import json
import sys
import os
from PIL import Image, ImageDraw, ImageEnhance, ImageFilter
import pytesseract
import io
import cv2
import numpy as np
from difflib import SequenceMatcher
import re
from typing import List, Dict, Tuple, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configure pytesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\softies\tesseract\tesseract.exe'

# Try to import additional OCR engines
try:
    import easyocr
    EASYOCR_AVAILABLE = True
    logger.info("EasyOCR available")
except ImportError:
    EASYOCR_AVAILABLE = False
    logger.warning("EasyOCR not available. Install with: pip install easyocr")

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
    logger.info("PaddleOCR available")
except ImportError:
    PADDLEOCR_AVAILABLE = False
    logger.warning("PaddleOCR not available. Install with: pip install paddlepaddle paddleocr")

class MultiOCRRedactor:
    def __init__(self, ssl_cert_path=None):
        self.dpi = 600
        self.confidence_threshold = 15
        self.ssl_cert_path = ssl_cert_path

        # Initialize OCR engines
        self.tesseract_configs = [
            r'--oem 3 --psm 6',
            r'--oem 3 --psm 8',
            r'--oem 3 --psm 7',
            r'--oem 3 --psm 13',
            r'--oem 1 --psm 6',
        ]

        if EASYOCR_AVAILABLE:
            try:
                self.easyocr_reader = easyocr.Reader(['en'], gpu=False)
                logger.info("EasyOCR initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize EasyOCR: {e}")
                self.easyocr_reader = None
        else:
            self.easyocr_reader = None

        if PADDLEOCR_AVAILABLE:
            # Try different initialization approaches for different PaddleOCR versions
            self.paddleocr_reader = None

            # Configure SSL settings for PaddleOCR model downloads
            self._configure_ssl_for_paddleocr()

            # Approach 1: Try with newer parameters
            try:
                self.paddleocr_reader = PaddleOCR(
                    use_textline_orientation=True,
                    lang='en',
                    use_gpu=False,
                    det_limit_side_len=960,  # Reduce memory usage
                    det_limit_type='max'
                )
                logger.info("PaddleOCR initialized with newer parameters")
            except Exception as e:
                logger.debug(f"Newer PaddleOCR initialization failed: {e}")

                # Approach 2: Try with older parameters
                try:
                    self.paddleocr_reader = PaddleOCR(
                        use_angle_cls=True,
                        lang='en',
                        use_gpu=False,
                        det_limit_side_len=960
                    )
                    logger.info("PaddleOCR initialized with older parameters")
                except Exception as e2:
                    logger.debug(f"Older PaddleOCR initialization failed: {e2}")

                    # Approach 3: Minimal initialization
                    try:
                        self.paddleocr_reader = PaddleOCR(lang='en', use_gpu=False)
                        logger.info("PaddleOCR initialized with minimal parameters")
                    except Exception:
                        logger.warning("All PaddleOCR initialization attempts failed.")
                        logger.warning("This is often due to SSL certificate or network issues.")
                        logger.warning("PaddleOCR will be disabled. EasyOCR and Tesseract will still work.")
                        self.paddleocr_reader = None
        else:
            self.paddleocr_reader = None

    def _configure_ssl_for_paddleocr(self):
        """Configure SSL settings to allow PaddleOCR model downloads."""
        import ssl
        import os
        import urllib3

        try:
            # If custom certificate path is provided, use it
            if self.ssl_cert_path and os.path.exists(self.ssl_cert_path):
                os.environ['REQUESTS_CA_BUNDLE'] = self.ssl_cert_path
                os.environ['CURL_CA_BUNDLE'] = self.ssl_cert_path
                os.environ['SSL_CERT_FILE'] = self.ssl_cert_path
                logger.info(f"Using custom certificate: {self.ssl_cert_path}")
            else:
                # Try to use certifi certificate bundle first
                try:
                    import certifi
                    certifi_path = certifi.where()
                    if os.path.exists(certifi_path):
                        os.environ['REQUESTS_CA_BUNDLE'] = certifi_path
                        os.environ['CURL_CA_BUNDLE'] = certifi_path
                        os.environ['SSL_CERT_FILE'] = certifi_path
                        logger.info(f"Using certifi certificate bundle: {certifi_path}")
                    else:
                        raise FileNotFoundError("Certifi bundle not found")
                except (ImportError, FileNotFoundError):
                    # Fallback: Disable SSL verification
                    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                    ssl._create_default_https_context = ssl._create_unverified_context
                    os.environ['CURL_CA_BUNDLE'] = ''
                    os.environ['REQUESTS_CA_BUNDLE'] = ''
                    os.environ['PYTHONHTTPSVERIFY'] = '0'
                    logger.warning("Certifi not available, SSL verification disabled")

            # Set shorter timeout for downloads
            os.environ['PADDLE_MODEL_TIMEOUT'] = '60'

        except Exception as e:
            logger.warning(f"Failed to configure SSL settings: {e}")

    def _call_paddleocr_method(self, img_array):
        """Call PaddleOCR with version-appropriate method."""
        if not self.paddleocr_reader:
            return None

        try:
            # Try newer predict method first
            if hasattr(self.paddleocr_reader, 'predict'):
                return self.paddleocr_reader.predict(img_array, cls=True)
            else:
                # Fall back to older ocr method (suppress deprecation warning)
                import warnings
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore", DeprecationWarning)
                    return self.paddleocr_reader.ocr(img_array, cls=True)
        except Exception as e:
            logger.warning(f"PaddleOCR method call failed: {e}")
            return None

    def preprocess_image_for_ocr(self, image: Image.Image) -> List[Image.Image]:
        """Enhanced image preprocessing for multiple OCR engines."""
        processed_images = []

        # Convert to numpy array
        img_array = np.array(image)
        if len(img_array.shape) == 3:
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_array

        # Method 1: Original image (for EasyOCR/PaddleOCR)
        processed_images.append(image)

        # Method 2: High contrast + OTSU
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_images.append(Image.fromarray(thresh))

        # Method 3: Adaptive threshold
        adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        processed_images.append(Image.fromarray(adaptive))

        # Method 4: Morphological operations
        kernel = np.ones((2, 2), np.uint8)
        morph = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        processed_images.append(Image.fromarray(morph))

        # Method 5: Denoising + sharpening
        denoised = cv2.fastNlMeansDenoising(gray)
        kernel_sharp = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel_sharp)
        _, thresh_sharp = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_images.append(Image.fromarray(thresh_sharp))

        return processed_images

    def extract_text_tesseract(self, image: Image.Image) -> List[Dict]:
        """Extract text using Tesseract with multiple configurations."""
        text_elements = []

        for config in self.tesseract_configs:
            try:
                ocr_data = pytesseract.image_to_data(
                    image, output_type=pytesseract.Output.DICT, config=config
                )

                for i in range(len(ocr_data['text'])):
                    text = ocr_data['text'][i].strip()
                    if not text or len(text) < 1:
                        continue

                    confidence = ocr_data['conf'][i]
                    if confidence < self.confidence_threshold:
                        continue

                    text_elements.append({
                        'text': text,
                        'confidence': confidence,
                        'coordinates': (ocr_data['left'][i], ocr_data['top'][i],
                                      ocr_data['width'][i], ocr_data['height'][i]),
                        'engine': f'tesseract_{config.split()[2] if len(config.split()) > 2 else "default"}'
                    })
            except Exception as e:
                logger.warning(f"Tesseract failed with config {config}: {e}")
                continue

        return text_elements

    def extract_text_easyocr(self, image: Image.Image) -> List[Dict]:
        """Extract text using EasyOCR."""
        if not self.easyocr_reader:
            return []

        text_elements = []
        try:
            # Convert PIL to numpy array
            img_array = np.array(image)

            results = self.easyocr_reader.readtext(img_array, detail=1)

            for (bbox, text, confidence) in results:
                if confidence < 0.2:  # EasyOCR uses 0-1 scale
                    continue

                # Convert bbox to x, y, w, h format
                x_coords = [point[0] for point in bbox]
                y_coords = [point[1] for point in bbox]

                x = int(min(x_coords))
                y = int(min(y_coords))
                w = int(max(x_coords) - min(x_coords))
                h = int(max(y_coords) - min(y_coords))

                text_elements.append({
                    'text': text.strip(),
                    'confidence': confidence * 100,  # Convert to 0-100 scale
                    'coordinates': (x, y, w, h),
                    'engine': 'easyocr'
                })

        except Exception as e:
            logger.warning(f"EasyOCR failed: {e}")

        return text_elements

    def extract_text_paddleocr(self, image: Image.Image) -> List[Dict]:
        """Extract text using PaddleOCR."""
        if not self.paddleocr_reader:
            return []

        text_elements = []
        try:
            # Convert PIL to numpy array
            img_array = np.array(image)

            # Use appropriate method based on PaddleOCR version
            results = self._call_paddleocr_method(img_array)

            if results and results[0]:
                for line in results[0]:
                    if not line:
                        continue

                    bbox, (text, confidence) = line

                    if confidence < 0.2:  # PaddleOCR uses 0-1 scale
                        continue

                    # Convert bbox to x, y, w, h format
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]

                    x = int(min(x_coords))
                    y = int(min(y_coords))
                    w = int(max(x_coords) - min(x_coords))
                    h = int(max(y_coords) - min(y_coords))

                    text_elements.append({
                        'text': text.strip(),
                        'confidence': confidence * 100,  # Convert to 0-100 scale
                        'coordinates': (x, y, w, h),
                        'engine': 'paddleocr'
                    })

        except Exception as e:
            logger.warning(f"PaddleOCR failed: {e}")

        return text_elements

    def extract_all_text_multi_engine(self, page: fitz.Page) -> Tuple[List[Dict], Image.Image]:
        """Extract text using all available OCR engines."""
        logger.info("Extracting text with multiple OCR engines...")

        # Convert page to image
        pix = page.get_pixmap(dpi=self.dpi)
        img_data = pix.tobytes("png")
        original_image = Image.open(io.BytesIO(img_data))

        # Get preprocessed images
        processed_images = self.preprocess_image_for_ocr(original_image)

        all_text_elements = []

        # Run each OCR engine on each preprocessed image
        for i, processed_img in enumerate(processed_images):
            # Tesseract (works better on binary images)
            if i > 0:  # Skip original image for Tesseract, use processed ones
                tesseract_results = self.extract_text_tesseract(processed_img)
                for result in tesseract_results:
                    result['preprocessing'] = f'method_{i}'
                all_text_elements.extend(tesseract_results)

            # EasyOCR (works better on original/lightly processed images)
            if i <= 2:  # Use original and first two processed images
                easyocr_results = self.extract_text_easyocr(processed_img)
                for result in easyocr_results:
                    result['preprocessing'] = f'method_{i}'
                all_text_elements.extend(easyocr_results)

            # PaddleOCR (works well on various image types)
            if i <= 3:  # Use original and first three processed images
                paddleocr_results = self.extract_text_paddleocr(processed_img)
                for result in paddleocr_results:
                    result['preprocessing'] = f'method_{i}'
                all_text_elements.extend(paddleocr_results)

        # Remove duplicates and merge results
        unique_elements = self._merge_ocr_results(all_text_elements)

        logger.info(f"Extracted {len(unique_elements)} unique text elements using multiple engines")
        return unique_elements, original_image

    def _merge_ocr_results(self, text_elements: List[Dict]) -> List[Dict]:
        """Merge results from multiple OCR engines, keeping the best detections."""
        if not text_elements:
            return []

        # Group similar detections
        merged_elements = []

        for element in text_elements:
            merged = False

            for existing in merged_elements:
                # Check if they're detecting the same text in roughly the same location
                text_similarity = SequenceMatcher(None,
                    element['text'].lower(),
                    existing['text'].lower()
                ).ratio()

                # Check coordinate proximity
                x1, y1, w1, h1 = element['coordinates']
                x2, y2, w2, h2 = existing['coordinates']

                center1_x, center1_y = x1 + w1/2, y1 + h1/2
                center2_x, center2_y = x2 + w2/2, y2 + h2/2

                distance = ((center1_x - center2_x)**2 + (center1_y - center2_y)**2)**0.5
                max_dimension = max(w1, h1, w2, h2)

                # If similar text and close location, merge them
                if text_similarity > 0.7 and distance < max_dimension:
                    # Keep the one with higher confidence or better engine
                    if self._is_better_detection(element, existing):
                        merged_elements.remove(existing)
                        merged_elements.append(element)
                    merged = True
                    break

            if not merged:
                merged_elements.append(element)

        return merged_elements

    def _is_better_detection(self, element1: Dict, element2: Dict) -> bool:
        """Determine which detection is better based on confidence and engine."""
        # Engine priority: PaddleOCR > EasyOCR > Tesseract
        engine_priority = {'paddleocr': 3, 'easyocr': 2, 'tesseract': 1}

        engine1_score = engine_priority.get(element1['engine'].split('_')[0], 0)
        engine2_score = engine_priority.get(element2['engine'].split('_')[0], 0)

        # If same engine, use confidence
        if engine1_score == engine2_score:
            return element1['confidence'] > element2['confidence']

        # Otherwise, use engine priority
        return engine1_score > engine2_score

    def find_text_matches_advanced(self, text_elements: List[Dict], target_text: str) -> List[Dict]:
        """Advanced text matching with multiple strategies."""
        logger.info(f"Searching for: '{target_text}' in {len(text_elements)} elements")
        matches = []

        for element in text_elements:
            detected_text = element['text']
            match_score = 0
            match_type = ""

            # Strategy 1: Exact match
            if target_text.lower() == detected_text.lower():
                match_score = 1.0
                match_type = "exact"

            # Strategy 2: Substring match
            elif target_text.lower() in detected_text.lower() or detected_text.lower() in target_text.lower():
                match_score = 0.95
                match_type = "substring"

            # Strategy 3: Fuzzy matching
            elif len(target_text) > 2:
                similarity = SequenceMatcher(None, target_text.lower(), detected_text.lower()).ratio()
                if similarity > 0.65:  # Lower threshold for multi-engine
                    match_score = similarity
                    match_type = f"fuzzy_{similarity:.2f}"

            # Strategy 4: Number/ID matching
            elif any(c.isdigit() for c in target_text):
                target_digits = re.sub(r'[^\w]', '', target_text.lower())
                detected_digits = re.sub(r'[^\w]', '', detected_text.lower())

                if target_digits in detected_digits or detected_digits in target_digits:
                    match_score = 0.9
                    match_type = "number_match"
                else:
                    # Character overlap for partial matches
                    common_chars = set(target_digits) & set(detected_digits)
                    if len(common_chars) > 0:
                        overlap_ratio = len(common_chars) / max(len(set(target_digits)), len(set(detected_digits)))
                        if overlap_ratio > 0.5:
                            match_score = overlap_ratio
                            match_type = f"char_overlap_{overlap_ratio:.2f}"

            # Strategy 5: Word matching for names/addresses
            elif len(target_text) > 4:
                target_words = set(re.findall(r'\w+', target_text.lower()))
                detected_words = set(re.findall(r'\w+', detected_text.lower()))

                if target_words and detected_words:
                    common_words = target_words & detected_words
                    if common_words:
                        word_ratio = len(common_words) / len(target_words)
                        if word_ratio > 0.4:  # Lower threshold for multi-engine
                            match_score = word_ratio
                            match_type = f"word_{word_ratio:.2f}"

            if match_score > 0:
                match_info = {
                    'target_text': target_text,
                    'detected_text': detected_text,
                    'match_score': match_score,
                    'match_type': match_type,
                    'confidence': element['confidence'],
                    'coordinates': element['coordinates'],
                    'engine': element['engine'],
                    'preprocessing': element.get('preprocessing', 'unknown')
                }
                matches.append(match_info)

        # Sort by match score, then confidence, then engine priority
        matches.sort(key=lambda x: (
            x['match_score'],
            x['confidence'],
            self._get_engine_priority(x['engine'])
        ), reverse=True)

        logger.info(f"Found {len(matches)} matches for '{target_text}'")
        for i, match in enumerate(matches[:3]):
            logger.info(f"  {i+1}. '{match['detected_text']}' - {match['match_type']} "
                       f"(conf: {match['confidence']:.1f}, engine: {match['engine']})")

        return matches

    def _get_engine_priority(self, engine: str) -> int:
        """Get priority score for engine."""
        if 'paddleocr' in engine:
            return 3
        elif 'easyocr' in engine:
            return 2
        elif 'tesseract' in engine:
            return 1
        return 0

    def apply_smart_redactions(self, page: fitz.Page, matches: List[Dict], original_image: Image.Image) -> int:
        """Apply redactions with smart filtering and padding."""
        if not matches:
            return 0

        # Filter overlapping matches
        filtered_matches = self._filter_overlapping_redactions(matches)

        redactions_applied = 0
        pdf_width, pdf_height = page.rect.width, page.rect.height
        img_width, img_height = original_image.size

        for match in filtered_matches[:2]:  # Apply top 2 matches max
            x, y, w, h = match['coordinates']

            # Convert to PDF coordinates
            scale_x = pdf_width / img_width
            scale_y = pdf_height / img_height

            x1 = x * scale_x
            y1 = y * scale_y
            x2 = (x + w) * scale_x
            y2 = (y + h) * scale_y

            # Smart padding based on text characteristics
            if any(c.isdigit() for c in match['target_text']):
                # Numbers need less padding
                padding_x = max(2, w * scale_x * 0.05)
                padding_y = max(1, h * scale_y * 0.05)
            else:
                # Text needs more padding
                padding_x = max(4, w * scale_x * 0.15)
                padding_y = max(3, h * scale_y * 0.15)

            x1 = max(0, x1 - padding_x)
            y1 = max(0, y1 - padding_y)
            x2 = min(pdf_width, x2 + padding_x)
            y2 = min(pdf_height, y2 + padding_y)

            # Create redaction
            redact_rect = fitz.Rect(x1, y1, x2, y2)
            page.add_redact_annot(redact_rect)

            logger.info(f"Redacted '{match['detected_text']}' using {match['engine']} "
                       f"at ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")
            redactions_applied += 1

        return redactions_applied

    def _filter_overlapping_redactions(self, matches: List[Dict]) -> List[Dict]:
        """Filter overlapping redactions to avoid over-redaction."""
        if not matches:
            return matches

        filtered = [matches[0]]  # Always keep the best match

        for match in matches[1:]:
            is_overlapping = False

            for existing in filtered:
                x1, y1, w1, h1 = match['coordinates']
                x2, y2, w2, h2 = existing['coordinates']

                # Calculate overlap
                overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
                overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
                overlap_area = overlap_x * overlap_y

                area1 = w1 * h1
                area2 = w2 * h2

                if overlap_area > 0.25 * min(area1, area2):
                    is_overlapping = True
                    break

            if not is_overlapping:
                filtered.append(match)

        return filtered

def main():
    if len(sys.argv) < 4 or len(sys.argv) > 5:
        print("Usage: python redact_multi_ocr.py <input_pdf> <output_pdf> <redaction_json> [ssl_cert_path]")
        print("\nThis script uses multiple OCR engines for maximum accuracy.")
        print("Optional dependencies for better results:")
        print("  pip install easyocr")
        print("  pip install paddlepaddle paddleocr")
        print("\nOptional SSL certificate path for PaddleOCR model downloads:")
        print("  python redact_multi_ocr.py input.pdf output.pdf targets.json C:\\path\\to\\cert.pem")
        print("\nTo configure SSL settings, run: python configure_ssl_cert.py")
        sys.exit(1)

    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    redaction_json = sys.argv[3]
    ssl_cert_path = sys.argv[4] if len(sys.argv) == 5 else None

    # Validate input files
    if not os.path.exists(input_pdf):
        logger.error(f"Input PDF not found: {input_pdf}")
        sys.exit(1)

    if not os.path.exists(redaction_json):
        logger.error(f"Redaction JSON not found: {redaction_json}")
        sys.exit(1)

    try:
        with open(redaction_json, "r") as f:
            redaction_data = json.load(f)
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing {redaction_json}: {e}")
        sys.exit(1)

    # Initialize the multi-OCR redactor with optional SSL certificate
    if ssl_cert_path:
        logger.info(f"Using SSL certificate: {ssl_cert_path}")
    redactor = MultiOCRRedactor(ssl_cert_path=ssl_cert_path)

    # Check which engines are available
    available_engines = ['tesseract']
    if redactor.easyocr_reader:
        available_engines.append('easyocr')
    if redactor.paddleocr_reader:
        available_engines.append('paddleocr')

    logger.info(f"Available OCR engines: {', '.join(available_engines)}")

    try:
        doc = fitz.open(input_pdf)
    except Exception as e:
        logger.error(f"Error opening PDF: {e}")
        sys.exit(1)

    # Group targets by page
    targets_by_page = {}
    for target in redaction_data:
        page_num = target.get("page", 1)
        if page_num not in targets_by_page:
            targets_by_page[page_num] = []
        targets_by_page[page_num].append(target)

    total_redactions = 0

    # Process each page
    for page_num in range(len(doc)):
        page = doc[page_num]
        page_targets = targets_by_page.get(page_num + 1, [])

        if not page_targets:
            continue

        logger.info(f"\n{'='*50}")
        logger.info(f"Processing page {page_num + 1} with {len(page_targets)} targets...")
        logger.info(f"{'='*50}")

        # Extract text using all available OCR engines
        text_elements, original_image = redactor.extract_all_text_multi_engine(page)

        # Process targets for this page
        for target in page_targets:
            if target["type"] in ["TEXT_EXACT", "TEXT_IN_IMAGE"]:
                content = target["content"]
                logger.info(f"\nSearching for: '{content}'")

                # Find matches using advanced matching
                matches = redactor.find_text_matches_advanced(text_elements, content)

                # Apply smart redactions
                if matches:
                    redactions_applied = redactor.apply_smart_redactions(page, matches, original_image)
                    total_redactions += redactions_applied
                    logger.info(f"✓ Applied {redactions_applied} redaction(s) for '{content}'")
                else:
                    logger.warning(f"✗ No matches found for '{content}'")

            elif target["type"] == "IMAGE_AREA":
                coords = target.get("bbox") or target.get("coordinates")
                if coords:
                    x1, y1, x2, y2 = coords
                    rect = fitz.Rect(x1, y1, x2, y2)
                    page.add_redact_annot(rect)
                    logger.info(f"✓ Image area redacted at ({x1}, {y1}, {x2}, {y2})")
                    total_redactions += 1

    # Apply all redactions
    logger.info(f"\n{'='*50}")
    logger.info("Applying all redactions to PDF...")
    for page in doc:
        page.apply_redactions()

    # Save the document
    try:
        doc.save(output_pdf, garbage=4, deflate=True, clean=True)
        doc.close()

        logger.info(f"{'='*50}")
        logger.info("🎉 MULTI-OCR REDACTION COMPLETE! 🎉")
        logger.info(f"{'='*50}")
        logger.info(f"Total redactions applied: {total_redactions}")
        logger.info(f"Output saved to: {output_pdf}")
        logger.info(f"Engines used: {', '.join(available_engines)}")

    except Exception as e:
        logger.error(f"Error saving output PDF: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
