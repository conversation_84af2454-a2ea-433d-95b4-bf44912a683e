"""
<PERSON><PERSON><PERSON> to get the complete certificate chain for PaddleOCR domain
and add all necessary certificates to the certifi bundle.
"""

import os
import sys
import ssl
import socket
import certifi
import subprocess
from urllib.parse import urlparse

def get_certificate_chain(hostname, port=443):
    """Get the complete certificate chain from the server."""
    print(f"🔗 Getting certificate chain from {hostname}:{port}...")
    
    try:
        # Create an unverified SSL context to get the full chain
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        # Connect and get the certificate chain
        with socket.create_connection((hostname, port), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                # Get the peer certificate chain
                cert_chain = ssock.getpeercert_chain()
                
                if not cert_chain:
                    print("❌ No certificate chain found")
                    return []
                
                print(f"✅ Found {len(cert_chain)} certificates in chain")
                
                # Convert each certificate to PEM format
                pem_certs = []
                for i, cert_der in enumerate(cert_chain):
                    cert_pem = ssl.DER_cert_to_PEM_cert(cert_der)
                    pem_certs.append(cert_pem)
                    
                    # Extract certificate info
                    cert_info = ssl.DER_cert_to_PEM_cert(cert_der)
                    print(f"  Certificate {i+1}: {len(cert_pem)} bytes")
                
                return pem_certs
                
    except Exception as e:
        print(f"❌ Failed to get certificate chain: {e}")
        return []

def get_cert_info_openssl(hostname, port=443):
    """Use OpenSSL command to get certificate chain if available."""
    print(f"🔧 Trying OpenSSL method for {hostname}:{port}...")
    
    try:
        # Try to use openssl command
        cmd = f'openssl s_client -connect {hostname}:{port} -showcerts -servername {hostname}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            output = result.stdout
            
            # Extract certificates from output
            certs = []
            cert_start = "-----BEGIN CERTIFICATE-----"
            cert_end = "-----END CERTIFICATE-----"
            
            lines = output.split('\n')
            current_cert = []
            in_cert = False
            
            for line in lines:
                if cert_start in line:
                    in_cert = True
                    current_cert = [line]
                elif cert_end in line:
                    current_cert.append(line)
                    cert_pem = '\n'.join(current_cert) + '\n'
                    certs.append(cert_pem)
                    current_cert = []
                    in_cert = False
                elif in_cert:
                    current_cert.append(line)
            
            print(f"✅ OpenSSL found {len(certs)} certificates")
            return certs
        else:
            print(f"❌ OpenSSL failed: {result.stderr}")
            return []
            
    except Exception as e:
        print(f"❌ OpenSSL method failed: {e}")
        return []

def backup_certifi_bundle():
    """Create a backup of the certifi bundle."""
    certifi_path = certifi.where()
    backup_path = certifi_path + ".backup"
    
    try:
        if not os.path.exists(backup_path):
            import shutil
            shutil.copy2(certifi_path, backup_path)
            print(f"✅ Backup created: {backup_path}")
        else:
            print(f"ℹ️  Backup already exists: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create backup: {e}")
        return False

def add_certificates_to_bundle(cert_chain, hostname):
    """Add all certificates in the chain to the certifi bundle."""
    certifi_path = certifi.where()
    
    try:
        # Read current bundle
        with open(certifi_path, 'r', encoding='utf-8') as f:
            current_content = f.read()
        
        # Add each certificate in the chain
        new_content = current_content
        added_count = 0
        
        for i, cert_pem in enumerate(cert_chain):
            # Check if certificate already exists
            if cert_pem.strip() in current_content:
                print(f"ℹ️  Certificate {i+1} already exists in bundle")
                continue
            
            # Add certificate to bundle
            cert_comment = f"\n# Certificate {i+1} for {hostname} (added for PaddleOCR)\n"
            new_content += cert_comment + cert_pem + "\n"
            added_count += 1
        
        if added_count > 0:
            # Write updated bundle
            with open(certifi_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ Added {added_count} certificates to certifi bundle")
            return True
        else:
            print("ℹ️  All certificates already present in bundle")
            return True
            
    except Exception as e:
        print(f"❌ Failed to add certificates to bundle: {e}")
        return False

def test_ssl_connection(hostname, port=443):
    """Test SSL connection after adding certificates."""
    print(f"\n🧪 Testing SSL connection to {hostname}:{port}...")
    
    try:
        import urllib.request
        url = f"https://{hostname}"
        
        # Test with updated certifi bundle
        response = urllib.request.urlopen(url, timeout=10)
        print(f"✅ SSL connection successful!")
        return True
        
    except Exception as e:
        print(f"❌ SSL connection still failing: {e}")
        return False

def main():
    print("="*70)
    print("GET COMPLETE CERTIFICATE CHAIN FOR PADDLEOCR")
    print("="*70)
    print("This script gets the complete certificate chain and adds it to certifi.")
    print()
    
    hostname = "paddle-model-ecology.bj.bcebos.com"
    print(f"Target server: {hostname}")
    print(f"Certifi bundle: {certifi.where()}")
    print()
    
    # Create backup
    if not backup_certifi_bundle():
        print("❌ Cannot proceed without backup")
        sys.exit(1)
    
    # Try to get certificate chain using Python SSL
    cert_chain = get_certificate_chain(hostname)
    
    # If that fails, try OpenSSL
    if not cert_chain:
        cert_chain = get_cert_info_openssl(hostname)
    
    if not cert_chain:
        print("❌ Could not retrieve certificate chain")
        print("\nManual steps:")
        print(f"1. Visit https://{hostname} in your browser")
        print("2. View certificate details")
        print("3. Export the complete certificate chain")
        print("4. Add each certificate to the certifi bundle manually")
        sys.exit(1)
    
    print(f"\n✅ Retrieved {len(cert_chain)} certificates")
    
    # Show certificate previews
    for i, cert in enumerate(cert_chain):
        print(f"\nCertificate {i+1} preview:")
        print(cert[:200] + "..." if len(cert) > 200 else cert)
    
    # Ask for confirmation
    response = input(f"\nAdd all {len(cert_chain)} certificates to certifi bundle? (y/n): ").strip().lower()
    if response != 'y':
        print("Operation cancelled")
        sys.exit(0)
    
    # Add certificates to bundle
    if add_certificates_to_bundle(cert_chain, hostname):
        print("\n✅ Certificates added successfully!")
        
        # Test the connection
        if test_ssl_connection(hostname):
            print("\n🎉 SUCCESS! SSL connection now works.")
            print("You can now run PaddleOCR without SSL issues.")
            
            # Test PaddleOCR initialization
            test_paddle = input("\nTest PaddleOCR initialization now? (y/n): ").strip().lower()
            if test_paddle == 'y':
                try:
                    print("Testing PaddleOCR...")
                    from paddleocr import PaddleOCR
                    ocr = PaddleOCR(lang='en')
                    print("🎉 PaddleOCR initialized successfully!")
                except Exception as e:
                    print(f"❌ PaddleOCR test failed: {e}")
        else:
            print("\n⚠️  Certificates added but connection still fails.")
            print("You may need to add additional root/intermediate certificates.")
    else:
        print("\n❌ Failed to add certificates")
    
    print(f"\nCertifi bundle location: {certifi.where()}")
    print("To restore original bundle: copy the .backup file back")

if __name__ == "__main__":
    main()
