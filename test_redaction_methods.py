"""
Test script to compare different PII redaction methods on scanned documents.
This helps you determine which approach works best for your specific documents.
"""

import os
import sys
import time
import json
from pathlib import Path
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RedactionTester:
    def __init__(self, input_pdf, redaction_json):
        self.input_pdf = input_pdf
        self.redaction_json = redaction_json
        self.test_results = {}
        
        # Available redaction scripts
        self.redaction_methods = {
            'original': 'redact.py',
            'optimized': 'redact_optimized_ocr.py',
            'advanced': 'redact_advanced_ocr.py',
            'hybrid': 'redact_hybrid.py',
            'image_based': 'redact_image_based.py',
            'enhanced': 'redact_enhanced_ocr.py',
            'multi_ocr': 'redact_multi_ocr.py'
        }
        
    def validate_inputs(self):
        """Validate input files exist."""
        if not os.path.exists(self.input_pdf):
            logger.error(f"Input PDF not found: {self.input_pdf}")
            return False
            
        if not os.path.exists(self.redaction_json):
            logger.error(f"Redaction JSON not found: {self.redaction_json}")
            return False
            
        return True
    
    def check_available_methods(self):
        """Check which redaction scripts are available."""
        available_methods = {}
        
        for method_name, script_name in self.redaction_methods.items():
            if os.path.exists(script_name):
                available_methods[method_name] = script_name
                logger.info(f"✓ {method_name}: {script_name} available")
            else:
                logger.warning(f"✗ {method_name}: {script_name} not found")
        
        return available_methods
    
    def run_redaction_method(self, method_name, script_name):
        """Run a specific redaction method and measure performance."""
        output_pdf = f"test_output_{method_name}.pdf"
        
        logger.info(f"\n{'='*50}")
        logger.info(f"Testing {method_name.upper()} method")
        logger.info(f"Script: {script_name}")
        logger.info(f"{'='*50}")
        
        # Prepare command
        command = [
            sys.executable, script_name,
            self.input_pdf, output_pdf, self.redaction_json
        ]
        
        # Measure execution time
        start_time = time.time()
        
        try:
            # Run the redaction script
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Check if output file was created
            output_exists = os.path.exists(output_pdf)
            output_size = os.path.getsize(output_pdf) if output_exists else 0
            
            # Parse output for redaction count
            redaction_count = self._parse_redaction_count(result.stdout)
            
            # Store results
            self.test_results[method_name] = {
                'success': result.returncode == 0,
                'execution_time': execution_time,
                'output_file': output_pdf,
                'output_exists': output_exists,
                'output_size': output_size,
                'redaction_count': redaction_count,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'return_code': result.returncode
            }
            
            if result.returncode == 0:
                logger.info(f"✓ {method_name} completed successfully")
                logger.info(f"  Execution time: {execution_time:.2f} seconds")
                logger.info(f"  Output file: {output_pdf} ({output_size} bytes)")
                logger.info(f"  Redactions applied: {redaction_count}")
            else:
                logger.error(f"✗ {method_name} failed with return code {result.returncode}")
                if result.stderr:
                    logger.error(f"  Error: {result.stderr}")
            
        except subprocess.TimeoutExpired:
            logger.error(f"✗ {method_name} timed out after 5 minutes")
            self.test_results[method_name] = {
                'success': False,
                'execution_time': 300,
                'error': 'Timeout',
                'output_exists': False
            }
        except Exception as e:
            logger.error(f"✗ {method_name} failed with exception: {e}")
            self.test_results[method_name] = {
                'success': False,
                'execution_time': 0,
                'error': str(e),
                'output_exists': False
            }
    
    def _parse_redaction_count(self, output_text):
        """Parse the number of redactions from script output."""
        if not output_text:
            return 0
            
        # Look for common patterns in output
        patterns = [
            "Total redactions applied:",
            "redactions applied:",
            "Applied redaction",
            "Redaction added"
        ]
        
        redaction_count = 0
        lines = output_text.split('\n')
        
        for line in lines:
            for pattern in patterns:
                if pattern in line:
                    # Try to extract number
                    words = line.split()
                    for word in words:
                        if word.isdigit():
                            redaction_count = max(redaction_count, int(word))
                            break
        
        return redaction_count
    
    def generate_report(self):
        """Generate a comprehensive test report."""
        logger.info(f"\n{'='*60}")
        logger.info("REDACTION METHOD COMPARISON REPORT")
        logger.info(f"{'='*60}")
        
        if not self.test_results:
            logger.info("No test results available.")
            return
        
        # Sort methods by success and execution time
        successful_methods = []
        failed_methods = []
        
        for method_name, results in self.test_results.items():
            if results['success']:
                successful_methods.append((method_name, results))
            else:
                failed_methods.append((method_name, results))
        
        # Sort successful methods by execution time
        successful_methods.sort(key=lambda x: x[1]['execution_time'])
        
        # Report successful methods
        if successful_methods:
            logger.info("\n🎉 SUCCESSFUL METHODS (sorted by speed):")
            logger.info("-" * 50)
            
            for i, (method_name, results) in enumerate(successful_methods, 1):
                logger.info(f"{i}. {method_name.upper()}")
                logger.info(f"   ⏱️  Time: {results['execution_time']:.2f} seconds")
                logger.info(f"   📄 Output: {results['output_file']} ({results['output_size']} bytes)")
                logger.info(f"   🎯 Redactions: {results['redaction_count']}")
                logger.info("")
        
        # Report failed methods
        if failed_methods:
            logger.info("\n❌ FAILED METHODS:")
            logger.info("-" * 50)
            
            for method_name, results in failed_methods:
                logger.info(f"• {method_name.upper()}")
                error_msg = results.get('error', 'Unknown error')
                logger.info(f"  Error: {error_msg}")
                if results.get('stderr'):
                    logger.info(f"  Details: {results['stderr'][:200]}...")
                logger.info("")
        
        # Recommendations
        logger.info("\n💡 RECOMMENDATIONS:")
        logger.info("-" * 50)
        
        if successful_methods:
            best_method = successful_methods[0][0]
            logger.info(f"🥇 Best overall: {best_method.upper()}")
            
            # Find method with most redactions
            max_redactions = max(results['redaction_count'] for _, results in successful_methods)
            most_accurate = [name for name, results in successful_methods 
                           if results['redaction_count'] == max_redactions]
            
            if most_accurate:
                logger.info(f"🎯 Most accurate: {', '.join(most_accurate).upper()}")
            
            # Speed vs accuracy trade-off
            if len(successful_methods) > 1:
                fastest = successful_methods[0]
                most_redactions = max(successful_methods, key=lambda x: x[1]['redaction_count'])
                
                if fastest[0] != most_redactions[0]:
                    logger.info(f"⚡ Fastest: {fastest[0].upper()} ({fastest[1]['execution_time']:.1f}s)")
                    logger.info(f"🎯 Most thorough: {most_redactions[0].upper()} ({most_redactions[1]['redaction_count']} redactions)")
        
        # Save detailed results to JSON
        report_file = "redaction_test_report.json"
        with open(report_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        logger.info(f"\n📊 Detailed results saved to: {report_file}")
    
    def cleanup_test_files(self):
        """Clean up test output files."""
        logger.info("\nCleaning up test files...")
        
        for method_name, results in self.test_results.items():
            if results.get('output_exists') and results.get('output_file'):
                try:
                    os.remove(results['output_file'])
                    logger.info(f"  Removed: {results['output_file']}")
                except Exception as e:
                    logger.warning(f"  Failed to remove {results['output_file']}: {e}")

def main():
    if len(sys.argv) != 3:
        print("Usage: python test_redaction_methods.py <input_pdf> <redaction_json>")
        print("\nThis script tests all available redaction methods and compares their performance.")
        print("Example: python test_redaction_methods.py pp_lic_lat.pdf lic_redact.json")
        sys.exit(1)
    
    input_pdf = sys.argv[1]
    redaction_json = sys.argv[2]
    
    # Initialize tester
    tester = RedactionTester(input_pdf, redaction_json)
    
    # Validate inputs
    if not tester.validate_inputs():
        sys.exit(1)
    
    # Check available methods
    available_methods = tester.check_available_methods()
    
    if not available_methods:
        logger.error("No redaction scripts found!")
        sys.exit(1)
    
    logger.info(f"\nTesting {len(available_methods)} redaction methods...")
    
    # Test each available method
    for method_name, script_name in available_methods.items():
        tester.run_redaction_method(method_name, script_name)
    
    # Generate comprehensive report
    tester.generate_report()
    
    # Ask if user wants to clean up test files
    response = input("\nDelete test output files? (y/n): ")
    if response.lower() == 'y':
        tester.cleanup_test_files()
    
    logger.info("\n🏁 Testing complete!")

if __name__ == "__main__":
    main()
