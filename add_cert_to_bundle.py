"""
<PERSON><PERSON><PERSON> to add a certificate to the certifi bundle for PaddleOCR downloads.
This script helps you add the specific certificate needed for paddle-model-ecology.bj.bcebos.com
"""

import os
import sys
import ssl
import socket
import certifi
from urllib.parse import urlparse

def get_server_certificate(hostname, port=443):
    """Get the certificate from the server."""
    print(f"Retrieving certificate from {hostname}:{port}...")
    
    try:
        # Create an unverified SSL context to get the certificate
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        # Connect and get certificate
        with socket.create_connection((hostname, port), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                cert_der = ssock.getpeercert(binary_form=True)
                cert_pem = ssl.DER_cert_to_PEM_cert(cert_der)
                return cert_pem
                
    except Exception as e:
        print(f"❌ Failed to retrieve certificate: {e}")
        return None

def backup_certifi_bundle():
    """Create a backup of the original certifi bundle."""
    certifi_path = certifi.where()
    backup_path = certifi_path + ".backup"
    
    try:
        if not os.path.exists(backup_path):
            import shutil
            shutil.copy2(certifi_path, backup_path)
            print(f"✅ Backup created: {backup_path}")
        else:
            print(f"ℹ️  Backup already exists: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create backup: {e}")
        return False

def add_certificate_to_bundle(cert_pem, hostname):
    """Add a certificate to the certifi bundle."""
    certifi_path = certifi.where()
    
    try:
        # Read current bundle
        with open(certifi_path, 'r', encoding='utf-8') as f:
            current_content = f.read()
        
        # Check if certificate already exists
        if cert_pem.strip() in current_content:
            print(f"ℹ️  Certificate for {hostname} already exists in bundle")
            return True
        
        # Add certificate to bundle
        cert_comment = f"\n# Certificate for {hostname}\n"
        new_content = current_content + cert_comment + cert_pem + "\n"
        
        # Write updated bundle
        with open(certifi_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ Certificate for {hostname} added to certifi bundle")
        return True
        
    except Exception as e:
        print(f"❌ Failed to add certificate to bundle: {e}")
        return False

def test_ssl_connection(hostname, port=443):
    """Test SSL connection with the updated bundle."""
    print(f"\n🧪 Testing SSL connection to {hostname}:{port}...")
    
    try:
        import urllib.request
        url = f"https://{hostname}"
        
        # Test with updated certifi bundle
        response = urllib.request.urlopen(url, timeout=10)
        print(f"✅ SSL connection successful to {hostname}")
        return True
        
    except Exception as e:
        print(f"❌ SSL connection failed: {e}")
        return False

def restore_backup():
    """Restore the original certifi bundle from backup."""
    certifi_path = certifi.where()
    backup_path = certifi_path + ".backup"
    
    try:
        if os.path.exists(backup_path):
            import shutil
            shutil.copy2(backup_path, certifi_path)
            print(f"✅ Certifi bundle restored from backup")
            return True
        else:
            print(f"❌ No backup found at: {backup_path}")
            return False
    except Exception as e:
        print(f"❌ Failed to restore backup: {e}")
        return False

def main():
    print("="*60)
    print("ADD CERTIFICATE TO CERTIFI BUNDLE")
    print("="*60)
    print("This script adds the PaddleOCR server certificate to your certifi bundle.")
    print()
    
    # PaddleOCR model server
    hostname = "paddle-model-ecology.bj.bcebos.com"
    
    print(f"Target server: {hostname}")
    print(f"Certifi bundle: {certifi.where()}")
    print()
    
    # Create backup
    if not backup_certifi_bundle():
        print("❌ Cannot proceed without backup")
        sys.exit(1)
    
    # Get server certificate
    cert_pem = get_server_certificate(hostname)
    if not cert_pem:
        print("❌ Cannot retrieve server certificate")
        sys.exit(1)
    
    print("✅ Certificate retrieved successfully")
    print(f"Certificate preview:\n{cert_pem[:200]}...")
    
    # Ask for confirmation
    response = input(f"\nAdd this certificate to certifi bundle? (y/n): ").strip().lower()
    if response != 'y':
        print("Operation cancelled")
        sys.exit(0)
    
    # Add certificate to bundle
    if add_certificate_to_bundle(cert_pem, hostname):
        print("\n✅ Certificate added successfully!")
        
        # Test the connection
        if test_ssl_connection(hostname):
            print("\n🎉 SUCCESS! SSL connection now works.")
            print("You can now run PaddleOCR without SSL issues.")
        else:
            print("\n⚠️  Certificate added but connection still fails.")
            print("You may need to restart your Python session.")
    else:
        print("\n❌ Failed to add certificate")
    
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    print(f"Certifi bundle: {certifi.where()}")
    print(f"Backup location: {certifi.where()}.backup")
    print("\nTo restore original bundle if needed:")
    print("python add_cert_to_bundle.py --restore")

def restore_mode():
    """Restore mode to revert changes."""
    print("="*60)
    print("RESTORE CERTIFI BUNDLE")
    print("="*60)
    
    if restore_backup():
        print("✅ Certifi bundle restored to original state")
    else:
        print("❌ Failed to restore certifi bundle")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--restore":
        restore_mode()
    else:
        main()
