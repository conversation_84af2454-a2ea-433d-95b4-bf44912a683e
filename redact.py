import fitz  # PyMuPDF
import json
import sys
from PIL import Image
import pytesseract
import io

# Configure pytesseract to use the Tesseract installation
pytesseract.pytesseract.tesseract_cmd = r'C:\softies\tesseract\tesseract.exe'

def find_text_in_image_and_redact(page, text_to_find):
    """
    Renders a PDF page as an image, uses OCR to find text,
    and adds redactions for all occurrences of the text.
    """
    print(f"DEBUG: Searching for text: '{text_to_find}'")

    # Render page to an image (pixmap) with higher DPI for better OCR
    pix = page.get_pixmap(dpi=600)  # Increased from 300 to 600
    img_data = pix.tobytes("png")
    img = Image.open(io.BytesIO(img_data))
    print(f"DEBUG: Image size: {img.size}")

    # Enhance image for better OCR
    # Convert to grayscale
    img = img.convert('L')

    # Enhance contrast and sharpness
    from PIL import ImageEnhance, ImageFilter

    # Increase contrast
    enhancer = ImageEnhance.Contrast(img)
    img = enhancer.enhance(2.0)

    # Sharpen the image
    img = img.filter(ImageFilter.SHARPEN)

    # Use OCR with better configuration
    custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz /-:.,()#'
    ocr_data = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT, config=custom_config)

    # Debug: Show all detected text
    detected_texts = []
    for i in range(len(ocr_data['text'])):
        text = ocr_data['text'][i].strip()
        if text and len(text) > 0:
            confidence = ocr_data['conf'][i]
            detected_texts.append(f"'{text}' (conf: {confidence})")

    print(f"DEBUG: OCR detected {len(detected_texts)} text elements:")
    for text in detected_texts[:15]:  # Show first 15 to see more results
        print(f"  {text}")
    if len(detected_texts) > 15:
        print(f"  ... and {len(detected_texts) - 15} more")

    matches_found = 0
    # Loop through all detected text boxes
    for i in range(len(ocr_data['text'])):
        detected_text = ocr_data['text'][i].strip()
        if not detected_text:
            continue

        # Check if the detected text contains the text we're looking for
        if text_to_find.lower() in detected_text.lower():
            matches_found += 1
            print(f"DEBUG: MATCH FOUND! Looking for '{text_to_find}' found in '{detected_text}'")

            # Get the coordinates and dimensions of the detected text
            x, y, w, h = ocr_data['left'][i], ocr_data['top'][i], ocr_data['width'][i], ocr_data['height'][i]
            print(f"DEBUG: OCR coordinates: x={x}, y={y}, w={w}, h={h}")

            # Convert image coordinates to PDF coordinates
            # The OCR coordinates are in pixels, so we need to scale them back to the PDF's point system
            img_width, img_height = img.size
            pdf_width, pdf_height = page.rect.width, page.rect.height

            x1 = (x / img_width) * pdf_width
            y1 = (y / img_height) * pdf_height
            x2 = ((x + w) / img_width) * pdf_width
            y2 = ((y + h) / img_height) * pdf_height

            print(f"DEBUG: PDF coordinates: x1={x1:.2f}, y1={y1:.2f}, x2={x2:.2f}, y2={y2:.2f}")

            # Create a redaction annotation
            redact_rect = fitz.Rect(x1, y1, x2, y2)
            page.add_redact_annot(redact_rect)
            print(f"DEBUG: Redaction added for '{detected_text}'")

    if matches_found == 0:
        print(f"DEBUG: NO MATCHES found for '{text_to_find}'")
    else:
        print(f"DEBUG: Total matches found: {matches_found}")

def redact_document(input_pdf_path, output_pdf_path, redaction_targets):
    doc = fitz.open(input_pdf_path)

    for target in redaction_targets:
        page_num = target["page"] - 1  # Convert to 0-based index
        if page_num >= len(doc):
            print(f"Warning: Page {target['page']} not found in document. Skipping.")
            continue

        page = doc[page_num]

        if target["type"] == "TEXT_EXACT":
            text_instances = page.search_for(target["content"])
            for inst in text_instances:
                page.add_redact_annot(inst)

        elif target["type"] == "IMAGE_AREA":
            # This type is now deprecated in favor of TEXT_IN_IMAGE, but we'll keep it for compatibility
            # Support both 'coordinates' and 'bbox' formats for flexibility
            coords = None
            if "coordinates" in target:
                coords = target["coordinates"]
            elif "bbox" in target:
                coords = target["bbox"]

            if coords:
                x1, y1, x2, y2 = coords
                rect = fitz.Rect(x1, y1, x2, y2)
                page.add_redact_annot(rect)
            else:
                print(f"Warning: IMAGE_AREA target on page {target['page']} is missing 'coordinates' or 'bbox'.")

        elif target["type"] == "TEXT_IN_IMAGE":
            find_text_in_image_and_redact(page, target["content"])

    # Apply all marked redactions
    for page in doc:
        page.apply_redactions()

    doc.save(output_pdf_path, garbage=4, deflate=True, clean=True)
    doc.close()

def main():
    if len(sys.argv) != 4:
        print("Usage: python redact.py <input_pdf> <output_pdf> <redaction_json>")
        sys.exit(1)

    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    redaction_json = sys.argv[3]

    try:
        with open(redaction_json, "r") as f:
            redaction_data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"Error reading or parsing {redaction_json}: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"Error: {redaction_json} not found.")
        sys.exit(1)

    redact_document(input_pdf, output_pdf, redaction_data)
    print(f"Redaction complete. Output saved to {output_pdf}")

if __name__ == "__main__":
    main()
