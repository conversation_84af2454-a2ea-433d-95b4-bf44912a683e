"""
<PERSON><PERSON><PERSON> to find exactly which certificate bundle PaddleOCR is using.
This will help identify the correct PEM file to modify.
"""

import os
import sys
import ssl
import certifi
import subprocess

def find_all_cert_bundles():
    """Find all possible certificate bundles on the system."""
    print("🔍 Searching for certificate bundles...")
    
    cert_locations = []
    
    # Standard locations
    possible_paths = [
        certifi.where(),  # certifi bundle
        os.path.join(os.path.dirname(sys.executable), 'lib', 'site-packages', 'certifi', 'cacert.pem'),
        os.path.join(os.path.dirname(sys.executable), 'Lib', 'site-packages', 'certifi', 'cacert.pem'),
        r'C:\Windows\System32\curl-ca-bundle.crt',
        r'C:\curl\bin\curl-ca-bundle.crt',
    ]
    
    # Environment variable paths
    env_vars = ['REQUESTS_CA_BUNDLE', 'CURL_CA_BUNDLE', 'SSL_CERT_FILE']
    for var in env_vars:
        if var in os.environ:
            possible_paths.append(os.environ[var])
    
    # Check which files exist
    for path in possible_paths:
        if path and os.path.exists(path):
            cert_locations.append(path)
            print(f"✅ Found: {path}")
        elif path:
            print(f"❌ Not found: {path}")
    
    return cert_locations

def check_paddle_imports():
    """Check what PaddleOCR imports and uses for SSL."""
    print("\n🔍 Analyzing PaddleOCR SSL dependencies...")
    
    try:
        # Import PaddleOCR modules to see what they use
        import paddle
        print(f"✅ Paddle location: {paddle.__file__}")
        
        # Check if paddle has its own SSL configuration
        paddle_dir = os.path.dirname(paddle.__file__)
        print(f"📁 Paddle directory: {paddle_dir}")
        
        # Look for certificate files in paddle directory
        for root, dirs, files in os.walk(paddle_dir):
            for file in files:
                if file.endswith(('.pem', '.crt', '.cert')):
                    cert_path = os.path.join(root, file)
                    print(f"🔐 Found cert in Paddle: {cert_path}")
        
    except ImportError:
        print("❌ PaddleOCR not installed")
    except Exception as e:
        print(f"❌ Error analyzing PaddleOCR: {e}")

def trace_ssl_calls():
    """Try to trace what SSL calls PaddleOCR makes."""
    print("\n🔍 Tracing SSL configuration...")
    
    # Check current SSL settings
    print(f"Default SSL context: {ssl.create_default_context()}")
    print(f"Default CA bundle: {ssl.get_default_verify_paths()}")
    
    # Check environment variables
    ssl_env_vars = [
        'REQUESTS_CA_BUNDLE', 'CURL_CA_BUNDLE', 'SSL_CERT_FILE', 
        'PYTHONHTTPSVERIFY', 'PADDLE_MODEL_TIMEOUT'
    ]
    
    print("\nEnvironment variables:")
    for var in ssl_env_vars:
        value = os.environ.get(var, 'Not set')
        print(f"  {var}: {value}")

def test_different_cert_paths():
    """Test SSL connection with different certificate paths."""
    print("\n🧪 Testing SSL with different certificate bundles...")
    
    cert_bundles = find_all_cert_bundles()
    
    for cert_path in cert_bundles:
        print(f"\nTesting with: {cert_path}")
        
        try:
            # Set environment variables
            os.environ['REQUESTS_CA_BUNDLE'] = cert_path
            os.environ['CURL_CA_BUNDLE'] = cert_path
            os.environ['SSL_CERT_FILE'] = cert_path
            
            # Create SSL context with this bundle
            context = ssl.create_default_context(cafile=cert_path)
            
            # Test connection
            import urllib.request
            url = "https://paddle-model-ecology.bj.bcebos.com"
            
            request = urllib.request.Request(url)
            response = urllib.request.urlopen(request, timeout=10, context=context)
            
            print(f"✅ SUCCESS with: {cert_path}")
            return cert_path
            
        except Exception as e:
            print(f"❌ Failed with {cert_path}: {str(e)[:100]}...")
    
    return None

def find_paddle_specific_certs():
    """Look for PaddleOCR-specific certificate configurations."""
    print("\n🔍 Looking for PaddleOCR-specific certificate configurations...")
    
    try:
        # Try to find where PaddleOCR stores its models
        paddle_home = os.path.expanduser("~/.paddlex")
        if os.path.exists(paddle_home):
            print(f"📁 Found PaddleOCR home: {paddle_home}")
            
            # Look for any certificate files
            for root, dirs, files in os.walk(paddle_home):
                for file in files:
                    if file.endswith(('.pem', '.crt', '.cert')):
                        cert_path = os.path.join(root, file)
                        print(f"🔐 Found cert in PaddleOCR home: {cert_path}")
        
        # Check site-packages for paddle-related certificates
        import site
        for site_dir in site.getsitepackages():
            paddle_dirs = [d for d in os.listdir(site_dir) if 'paddle' in d.lower()]
            for paddle_dir in paddle_dirs:
                paddle_path = os.path.join(site_dir, paddle_dir)
                if os.path.isdir(paddle_path):
                    print(f"📁 Found Paddle package: {paddle_path}")
                    
                    # Look for certificates
                    for root, dirs, files in os.walk(paddle_path):
                        for file in files:
                            if file.endswith(('.pem', '.crt', '.cert')):
                                cert_path = os.path.join(root, file)
                                print(f"🔐 Found cert in Paddle package: {cert_path}")
    
    except Exception as e:
        print(f"❌ Error searching for Paddle certs: {e}")

def main():
    print("="*70)
    print("FIND PADDLEOCR CERTIFICATE BUNDLE")
    print("="*70)
    print("This script helps identify which certificate bundle PaddleOCR is using.")
    print()
    
    # Find all certificate bundles
    cert_bundles = find_all_cert_bundles()
    
    # Check PaddleOCR imports
    check_paddle_imports()
    
    # Look for PaddleOCR-specific certificates
    find_paddle_specific_certs()
    
    # Trace SSL configuration
    trace_ssl_calls()
    
    # Test different certificate paths
    working_cert = test_different_cert_paths()
    
    print("\n" + "="*70)
    print("SUMMARY")
    print("="*70)
    
    if working_cert:
        print(f"✅ Working certificate bundle: {working_cert}")
        print(f"\nTo fix PaddleOCR SSL issues, add the certificate to: {working_cert}")
    else:
        print("❌ No working certificate bundle found")
        print("\nTry these approaches:")
        print("1. Add certificate to all found bundles")
        print("2. Use SSL bypass mode")
        print("3. Check if PaddleOCR uses a different HTTP client")
    
    print(f"\nFound certificate bundles:")
    for cert in cert_bundles:
        print(f"  - {cert}")

if __name__ == "__main__":
    main()
