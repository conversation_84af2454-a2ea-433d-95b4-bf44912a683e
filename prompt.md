Analyze the attached PDF document for all instances of Personally Identifiable Information (PII) and other sensitive or identifying data. Identify and list all names, addresses, dates, phone numbers, email addresses, account numbers, policy numbers, tracking numbers, unique IDs, and any other information that could be considered PII or sensitive. Also, identify and list any map provider attributions or similar identifying text found within images.

For each identified item, provide its content, the 1-based page number it appears on, and its type. Use the following types:
*   `TEXT_EXACT`: For exact text strings found in searchable text.
*   `TEXT_REGEX`: For patterns like phone numbers, email addresses, or specific date formats.
*   `TEXT_IN_IMAGE`: For text that appears within an image (e.g., map labels, logos in photos).
*   `IMAGE_AREA`: For non-textual PII within an image (e.g., faces, signatures, barcodes).

For `IMAGE_AREA` types, provide approximate bounding box coordinates `[x1, y1, x2, y2]` relative to the page, assuming a top-left origin.

For `TEXT_EXACT`, `TEXT_REGEX`, and `TEXT_IN_IMAGE` types, if the context is not immediately clear from the content itself (e.g., a number that could be a policy ID or a tracking number), include a `context` field (e.g., `"context": "Policy Number"`).

Ensure the JSON output contains only the data, with no comments or extraneous text, following the exact structure of the example provided below:

```json
[
  { "type": "TEXT_EXACT", "content": "ROSEFILDA MOISES", "page": 1 },
  { "type": "TEXT_EXACT", "content": "703 Passaic Av", "page": 1 },
  { "type": "TEXT_EXACT", "content": "Clifton, NJ 07012", "page": 1 },
  { "type": "TEXT_EXACT", "content": "5171243127", "page": 1, "context": "Myriad Tracking#" },
  { "type": "TEXT_EXACT", "content": "NJH00002147778", "page": 1, "context": "Policy Number" },
  { "type": "TEXT_EXACT", "content": "NJH00002147778", "page": 1, "context": "Project Name" },
  { "type": "TEXT_EXACT", "content": "MOISES RODULFO", "page": 2 },
  { "type": "TEXT_EXACT", "content": "703 PASSAIC AVE", "page": 2 },
  { "type": "TEXT_EXACT", "content": "CLIFTON", "page": 2 },
  { "type": "TEXT_EXACT", "content": "NJ", "page": 2 },
  { "type": "TEXT_EXACT", "content": "07012", "page": 2 },
  { "type": "TEXT_EXACT", "content": "CLIFTON NJ", "page": 2 },
  { "type": "TEXT_EXACT", "content": "340398", "page": 3, "context": "Community ID" },
  { "type": "TEXT_EXACT", "content": "CLIFTON, CITY OF", "page": 3, "context": "Community Name" },
  { "type": "TEXT_EXACT", "content": "Clifton Fire Department Engine 4", "page": 3, "context": "Department Name" },
  { "type": "TEXT_EXACT", "content": "Clifton Fire Response Area", "page": 3, "context": "Responding Station" },
  { "type": "TEXT_EXACT", "content": "703 Passaic Av", "page": 7, "context": "Property Address" },
  { "type": "TEXT_EXACT", "content": "Clifton, NJ 07012", "page": 7, "context": "Property Address" },
  { "type": "TEXT_IN_IMAGE", "content": "Bing", "page": 9 },
  { "type": "TEXT_IN_IMAGE", "content": "nearmap", "page": 10 },
  { "type": "TEXT_IN_IMAGE", "content": "nearmap", "page": 11 },
  { "type": "TEXT_IN_IMAGE", "content": "nearmap", "page": 12 },
  { "type": "TEXT_IN_IMAGE", "content": "nearmap", "page": 13 },
  { "type": "TEXT_EXACT", "content": "Lehigh Ave", "page": 14 },
  { "type": "TEXT_EXACT", "content": "Passaic Ave", "page": 14 },
  { "type": "TEXT_EXACT", "content": "Patricia Pl", "page": 14 },
  { "type": "TEXT_EXACT", "content": "Annabelle Ave", "page": 14 },
  { "type": "TEXT_EXACT", "content": "Lorrie Ln", "page": 14 },
  { "type": "TEXT_EXACT", "content": "Lorrie Li", "page": 14 },
  { "type": "TEXT_EXACT", "content": "Annabe\"", "page": 14 },
  { "type": "TEXT_EXACT", "content": "Google", "page": 14 },
  { "type": "TEXT_EXACT", "content": "Map data ©2023 Google", "page": 14 }
]
```