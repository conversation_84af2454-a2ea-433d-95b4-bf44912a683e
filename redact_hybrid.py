import fitz  # PyMuPDF
import json
import sys
from PIL import Image, ImageDraw, ImageEnhance, ImageFilter
import pytesseract
import io
from difflib import SequenceMatcher

# Configure pytesseract to use the Tesseract installation
pytesseract.pytesseract.tesseract_cmd = r'C:\softies\tesseract\tesseract.exe'

def detect_page_type(page):
    """
    Detect if a page is text-based or image-based by analyzing extractable text.
    Returns 'text' for text-based pages, 'image' for image/scanned pages.
    """
    try:
        # Extract text from the page
        text = page.get_text()

        # Count meaningful characters (letters, numbers, common punctuation)
        meaningful_chars = sum(1 for c in text if c.isalnum() or c in ' .,!?-()[]{}:;')

        # If we have substantial readable text, it's likely a text-based PDF
        if meaningful_chars > 100:  # Threshold for meaningful text
            print(f"DEBUG: Page detected as TEXT-BASED ({meaningful_chars} meaningful characters)")
            return 'text'
        else:
            print(f"DEBUG: Page detected as IMAGE-BASED ({meaningful_chars} meaningful characters)")
            return 'image'
    except:
        print("DEBUG: Page detection failed, defaulting to IMAGE-BASED")
        return 'image'

def redact_text_based(page, text_to_find):
    """
    Use PyMuPDF's native text search for text-based PDFs.
    Returns number of matches found.
    """
    print(f"DEBUG: Text-based search for: '{text_to_find}'")

    # Search for exact text matches
    text_instances = page.search_for(text_to_find)

    if text_instances:
        print(f"DEBUG: Found {len(text_instances)} exact matches")
        for rect in text_instances:
            page.add_redact_annot(rect)
        return len(text_instances)

    # If no exact matches, try case-insensitive search
    all_text = page.get_text()
    if text_to_find.lower() in all_text.lower():
        # Find all case-insensitive matches
        text_instances = page.search_for(text_to_find, hit_max=50)  # Increase hit limit
        if not text_instances:
            # Try different case variations
            variations = [
                text_to_find.upper(),
                text_to_find.lower(),
                text_to_find.title(),
            ]
            for variation in variations:
                text_instances = page.search_for(variation)
                if text_instances:
                    print(f"DEBUG: Found {len(text_instances)} matches with variation '{variation}'")
                    for rect in text_instances:
                        page.add_redact_annot(rect)
                    return len(text_instances)

    print(f"DEBUG: No text-based matches found for '{text_to_find}'")
    return 0

def redact_image_based(page, text_to_find):
    """
    Use OCR-based redaction for image/scanned pages.
    Returns the modified page and number of matches found.
    """
    print(f"DEBUG: Image-based OCR search for: '{text_to_find}'")

    # Convert page to image
    pix = page.get_pixmap(dpi=300)
    img_data = pix.tobytes("png")
    image = Image.open(io.BytesIO(img_data))

    # Enhance image for better OCR
    img_enhanced = image.convert('L')  # Convert to grayscale
    enhancer = ImageEnhance.Contrast(img_enhanced)
    img_enhanced = enhancer.enhance(1.5)

    # Use OCR to get text and coordinates
    custom_config = r'--oem 3 --psm 6'
    ocr_data = pytesseract.image_to_data(img_enhanced, output_type=pytesseract.Output.DICT, config=custom_config)

    matches_found = 0

    # Loop through OCR results
    for i in range(len(ocr_data['text'])):
        detected_text = ocr_data['text'][i].strip()
        if not detected_text:
            continue

        match_found = False
        match_type = ""

        # Method 1: Exact substring match
        if text_to_find.lower() in detected_text.lower():
            match_found = True
            match_type = "exact substring"

        # Method 2: Fuzzy matching for OCR errors
        elif len(text_to_find) > 3:
            similarity = SequenceMatcher(None, text_to_find.lower(), detected_text.lower()).ratio()
            if similarity > 0.8:  # 80% similarity threshold
                match_found = True
                match_type = f"fuzzy match ({similarity:.2f})"

        if match_found:
            matches_found += 1
            print(f"DEBUG: OCR MATCH! '{text_to_find}' found in '{detected_text}' ({match_type})")

            # Get coordinates and convert to PDF coordinates
            x, y, w, h = ocr_data['left'][i], ocr_data['top'][i], ocr_data['width'][i], ocr_data['height'][i]

            # Convert image coordinates to PDF coordinates
            img_width, img_height = image.size
            pdf_width, pdf_height = page.rect.width, page.rect.height

            x1 = (x / img_width) * pdf_width
            y1 = (y / img_height) * pdf_height
            x2 = ((x + w) / img_width) * pdf_width
            y2 = ((y + h) / img_height) * pdf_height

            # Add padding
            padding = 2
            x1 = max(0, x1 - padding)
            y1 = max(0, y1 - padding)
            x2 = min(pdf_width, x2 + padding)
            y2 = min(pdf_height, y2 + padding)

            # Create redaction annotation
            redact_rect = fitz.Rect(x1, y1, x2, y2)
            page.add_redact_annot(redact_rect)

    if matches_found == 0:
        print(f"DEBUG: No OCR matches found for '{text_to_find}'")
    else:
        print(f"DEBUG: Total OCR matches: {matches_found}")

    return matches_found

def redact_document_hybrid(input_pdf_path, output_pdf_path, redaction_targets):
    """
    Hybrid redaction: Use text-based for text PDFs, image-based for scanned PDFs.
    """
    doc = fitz.open(input_pdf_path)

    # Group targets by page for efficiency
    targets_by_page = {}
    for target in redaction_targets:
        page_num = target.get("page", 1)
        if page_num not in targets_by_page:
            targets_by_page[page_num] = []
        targets_by_page[page_num].append(target)

    # Process each page
    for page_num in range(len(doc)):
        page = doc[page_num]
        page_targets = targets_by_page.get(page_num + 1, [])

        if not page_targets:
            continue

        print(f"\nProcessing page {page_num + 1}...")

        # Detect page type
        page_type = detect_page_type(page)

        # Process targets for this page
        for target in page_targets:
            target_type = target["type"]

            if target_type == "TEXT_EXACT":
                content = target["content"]
                if page_type == 'text':
                    # Use text-based redaction for text PDFs
                    matches = redact_text_based(page, content)
                    if matches == 0:
                        # Fallback to OCR if text search fails
                        print("DEBUG: Text search failed, trying OCR fallback...")
                        redact_image_based(page, content)
                else:
                    # Use OCR for image-based pages
                    redact_image_based(page, content)

            elif target_type == "TEXT_IN_IMAGE":
                # Always use OCR for TEXT_IN_IMAGE (explicitly for images)
                content = target["content"]
                redact_image_based(page, content)

            elif target_type == "IMAGE_AREA":
                # Handle coordinate-based redaction
                coords = None
                if "bbox" in target:
                    coords = target["bbox"]
                elif "coordinates" in target:
                    coords = target["coordinates"]

                if coords:
                    x1, y1, x2, y2 = coords
                    rect = fitz.Rect(x1, y1, x2, y2)
                    page.add_redact_annot(rect)
                    print(f"DEBUG: Image area redacted at ({x1}, {y1}, {x2}, {y2})")
                else:
                    print(f"Warning: IMAGE_AREA target on page {target['page']} is missing coordinates.")

    # Apply all redactions
    for page in doc:
        page.apply_redactions()

    # Save the redacted document
    doc.save(output_pdf_path, garbage=4, deflate=True, clean=True)
    doc.close()

    print(f"\nHybrid redaction complete. Output saved to: {output_pdf_path}")

def main():
    if len(sys.argv) != 4:
        print("Usage: python redact_hybrid.py <input_pdf> <output_pdf> <redaction_json>")
        sys.exit(1)

    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    redaction_json = sys.argv[3]

    try:
        with open(redaction_json, "r") as f:
            redaction_data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"Error reading or parsing {redaction_json}: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"Error: {redaction_json} not found.")
        sys.exit(1)

    redact_document_hybrid(input_pdf, output_pdf, redaction_data)

if __name__ == "__main__":
    main()
