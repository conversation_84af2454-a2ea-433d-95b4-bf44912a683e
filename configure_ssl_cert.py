"""
SSL Certificate Configuration Script for PaddleOCR
This script helps you configure SSL certificates for PaddleOCR model downloads.
"""

import os
import sys
import ssl
import urllib.request
import certifi

def test_ssl_connection():
    """Test SSL connection to PaddleOCR model server."""
    url = "https://paddle-model-ecology.bj.bcebos.com"

    print(f"Testing SSL connection to: {url}")

    try:
        # Test with default SSL context
        response = urllib.request.urlopen(url, timeout=10)
        print("✓ SSL connection successful with default settings")
        return True
    except Exception as e:
        print(f"✗ SSL connection failed: {e}")
        return False

def configure_ssl_bypass():
    """Configure SSL bypass for PaddleOCR."""
    print("\nConfiguring SSL bypass...")

    try:
        # Create unverified SSL context
        ssl._create_default_https_context = ssl._create_unverified_context

        # Set environment variables
        os.environ['CURL_CA_BUNDLE'] = ''
        os.environ['REQUESTS_CA_BUNDLE'] = ''
        os.environ['PYTHONHTTPSVERIFY'] = '0'

        print("✓ SSL bypass configured")

        # Test connection again
        url = "https://paddle-model-ecology.bj.bcebos.com"
        try:
            response = urllib.request.urlopen(url, timeout=10)
            print("✓ SSL connection now working with bypass")
            return True
        except Exception as e:
            print(f"✗ SSL connection still failing: {e}")
            return False

    except Exception as e:
        print(f"✗ Failed to configure SSL bypass: {e}")
        return False

def configure_certifi_certificate():
    """Configure certifi certificate bundle."""
    print("\nConfiguring certifi certificate bundle...")

    try:
        import certifi
        cert_path = certifi.where()
        print(f"Certifi bundle location: {cert_path}")

        if not os.path.exists(cert_path):
            print(f"✗ Certifi bundle not found: {cert_path}")
            return False

        # Set certificate bundle path
        os.environ['REQUESTS_CA_BUNDLE'] = cert_path
        os.environ['CURL_CA_BUNDLE'] = cert_path
        os.environ['SSL_CERT_FILE'] = cert_path

        print("✓ Certifi certificate configured")

        # Test connection
        url = "https://paddle-model-ecology.bj.bcebos.com"
        try:
            response = urllib.request.urlopen(url, timeout=10)
            print("✓ SSL connection working with certifi certificate")
            return True
        except Exception as e:
            print(f"✗ SSL connection still failing: {e}")
            return False

    except ImportError:
        print("✗ Certifi package not installed")
        return False
    except Exception as e:
        print(f"✗ Failed to configure certifi certificate: {e}")
        return False

def configure_custom_certificate(cert_path):
    """Configure custom certificate for SSL."""
    print(f"\nConfiguring custom certificate: {cert_path}")

    if not os.path.exists(cert_path):
        print(f"✗ Certificate file not found: {cert_path}")
        return False

    try:
        # Set certificate bundle path
        os.environ['REQUESTS_CA_BUNDLE'] = cert_path
        os.environ['CURL_CA_BUNDLE'] = cert_path
        os.environ['SSL_CERT_FILE'] = cert_path

        print("✓ Custom certificate configured")

        # Test connection
        url = "https://paddle-model-ecology.bj.bcebos.com"
        try:
            response = urllib.request.urlopen(url, timeout=10)
            print("✓ SSL connection working with custom certificate")
            return True
        except Exception as e:
            print(f"✗ SSL connection still failing: {e}")
            return False

    except Exception as e:
        print(f"✗ Failed to configure custom certificate: {e}")
        return False

def get_system_certificates():
    """Get information about system certificates."""
    print("\nSystem Certificate Information:")
    print("-" * 40)

    # Check certifi bundle
    try:
        certifi_path = certifi.where()
        print(f"Certifi bundle: {certifi_path}")
        print(f"Certifi exists: {os.path.exists(certifi_path)}")
    except Exception as e:
        print(f"Certifi error: {e}")

    # Check environment variables
    cert_vars = ['REQUESTS_CA_BUNDLE', 'CURL_CA_BUNDLE', 'SSL_CERT_FILE']
    for var in cert_vars:
        value = os.environ.get(var, 'Not set')
        print(f"{var}: {value}")

    # Check default SSL context
    try:
        context = ssl.create_default_context()
        print(f"Default SSL context: {context}")
        print(f"CA certs loaded: {context.ca_certs is not None}")
    except Exception as e:
        print(f"SSL context error: {e}")

def create_certificate_config_file():
    """Create a configuration file for certificate settings."""
    config_content = """# SSL Certificate Configuration for PaddleOCR
# Uncomment and modify the appropriate line below:

# Option 1: Use custom certificate file
# REQUESTS_CA_BUNDLE=C:\\path\\to\\your\\certificate.pem
# CURL_CA_BUNDLE=C:\\path\\to\\your\\certificate.pem

# Option 2: Disable SSL verification (less secure)
# PYTHONHTTPSVERIFY=0
# REQUESTS_CA_BUNDLE=
# CURL_CA_BUNDLE=

# Option 3: Use system certificates
# REQUESTS_CA_BUNDLE=C:\\Windows\\System32\\curl-ca-bundle.crt
"""

    config_file = "ssl_config.env"
    with open(config_file, 'w') as f:
        f.write(config_content)

    print(f"\n✓ Created configuration template: {config_file}")
    print("Edit this file with your certificate path and run:")
    print("set /p < ssl_config.env")

def main():
    print("="*60)
    print("SSL CERTIFICATE CONFIGURATION FOR PADDLEOCR")
    print("="*60)

    # Get system certificate info
    get_system_certificates()

    # Test initial connection
    if test_ssl_connection():
        print("\n🎉 SSL is already working! No configuration needed.")
        return

    print("\nSSL connection failed. Choose a configuration option:")
    print("1. Use certifi certificate bundle (recommended)")
    print("2. Configure SSL bypass (disable verification)")
    print("3. Use custom certificate file")
    print("4. Create configuration template")
    print("5. Exit")

    choice = input("\nEnter your choice (1-5): ").strip()

    if choice == "1":
        if configure_certifi_certificate():
            print("\n✓ Certifi certificate configured successfully!")
            print("You can now run the PaddleOCR redaction script.")
        else:
            print("\n✗ Certifi certificate configuration failed.")

    elif choice == "2":
        if configure_ssl_bypass():
            print("\n✓ SSL bypass configured successfully!")
            print("You can now run the PaddleOCR redaction script.")
        else:
            print("\n✗ SSL bypass configuration failed.")

    elif choice == "3":
        cert_path = input("Enter path to certificate file: ").strip()
        if configure_custom_certificate(cert_path):
            print("\n✓ Custom certificate configured successfully!")
            print("You can now run the PaddleOCR redaction script.")
        else:
            print("\n✗ Custom certificate configuration failed.")

    elif choice == "4":
        create_certificate_config_file()

    elif choice == "5":
        print("Exiting...")

    else:
        print("Invalid choice.")

    print("\nNote: These settings are temporary for this session.")
    print("For permanent configuration, add the environment variables to your system.")

if __name__ == "__main__":
    main()
