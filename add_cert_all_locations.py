"""
Add certificate to ALL possible certificate bundle locations.
This ensures PaddleOCR can find the certificate regardless of which bundle it uses.
"""

import os
import sys
import ssl
import certifi
import socket

def get_server_certificate(hostname, port=443):
    """Get the server certificate."""
    print(f"🔐 Getting certificate from {hostname}:{port}...")
    
    try:
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        with socket.create_connection((hostname, port), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                cert_der = ssock.getpeercert(binary_form=True)
                cert_pem = ssl.DER_cert_to_PEM_cert(cert_der)
                return cert_pem
                
    except Exception as e:
        print(f"❌ Failed to get certificate: {e}")
        return None

def get_all_cert_locations():
    """Get all possible certificate bundle locations."""
    locations = []
    
    # 1. Certifi bundles (found multiple in your system)
    certifi_locations = [
        r"C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.2\Lib\site-packages\certifi\cacert.pem",
        r"C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.2\lib\site-packages\certifi\cacert.pem"
    ]
    
    # 2. System OpenSSL locations (from your output)
    system_locations = [
        r"C:\Program Files\Common Files\SSL\cert.pem",
        r"C:\Program Files\Common Files\SSL\certs",
    ]
    
    # 3. Python SSL default paths
    ssl_paths = ssl.get_default_verify_paths()
    if ssl_paths.openssl_cafile:
        system_locations.append(ssl_paths.openssl_cafile)
    if ssl_paths.openssl_capath:
        system_locations.append(ssl_paths.openssl_capath)
    
    # 4. Environment variable paths
    env_paths = []
    for var in ['REQUESTS_CA_BUNDLE', 'CURL_CA_BUNDLE', 'SSL_CERT_FILE']:
        if var in os.environ:
            env_paths.append(os.environ[var])
    
    # Combine all locations
    all_locations = certifi_locations + system_locations + env_paths
    
    # Check which ones exist or can be created
    valid_locations = []
    for location in all_locations:
        if location:
            if os.path.exists(location):
                valid_locations.append(location)
                print(f"✅ Found: {location}")
            else:
                # Check if parent directory exists (for files we can create)
                parent_dir = os.path.dirname(location)
                if os.path.exists(parent_dir):
                    valid_locations.append(location)
                    print(f"📝 Can create: {location}")
                else:
                    print(f"❌ Cannot access: {location}")
    
    return valid_locations

def backup_file(file_path):
    """Create backup of a certificate file."""
    backup_path = file_path + ".backup"
    try:
        if os.path.exists(file_path) and not os.path.exists(backup_path):
            import shutil
            shutil.copy2(file_path, backup_path)
            print(f"  📋 Backup created: {backup_path}")
        return True
    except Exception as e:
        print(f"  ❌ Backup failed: {e}")
        return False

def add_cert_to_file(cert_pem, file_path, hostname):
    """Add certificate to a specific file."""
    print(f"\n📝 Adding certificate to: {file_path}")
    
    try:
        # Create parent directory if needed
        parent_dir = os.path.dirname(file_path)
        if not os.path.exists(parent_dir):
            os.makedirs(parent_dir, exist_ok=True)
            print(f"  📁 Created directory: {parent_dir}")
        
        # Backup existing file
        if os.path.exists(file_path):
            backup_file(file_path)
            
            # Read existing content
            with open(file_path, 'r', encoding='utf-8') as f:
                current_content = f.read()
        else:
            current_content = ""
            print(f"  📄 Creating new file: {file_path}")
        
        # Check if certificate already exists
        if cert_pem.strip() in current_content:
            print(f"  ℹ️  Certificate already exists in {file_path}")
            return True
        
        # Add certificate
        cert_comment = f"\n# Certificate for {hostname} (added for PaddleOCR)\n"
        new_content = current_content + cert_comment + cert_pem + "\n"
        
        # Write updated content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"  ✅ Certificate added to {file_path}")
        return True
        
    except Exception as e:
        print(f"  ❌ Failed to add certificate to {file_path}: {e}")
        return False

def test_ssl_with_each_bundle(hostname, cert_locations):
    """Test SSL connection using each certificate bundle."""
    print(f"\n🧪 Testing SSL connection with each bundle...")
    
    working_bundles = []
    
    for cert_path in cert_locations:
        if not os.path.exists(cert_path):
            continue
            
        print(f"\nTesting: {cert_path}")
        
        try:
            # Set environment to use this specific bundle
            os.environ['REQUESTS_CA_BUNDLE'] = cert_path
            os.environ['CURL_CA_BUNDLE'] = cert_path
            os.environ['SSL_CERT_FILE'] = cert_path
            
            # Create SSL context with this bundle
            context = ssl.create_default_context(cafile=cert_path)
            
            # Test connection
            import urllib.request
            url = f"https://{hostname}"
            response = urllib.request.urlopen(url, timeout=10, context=context)
            
            print(f"  ✅ SUCCESS with: {cert_path}")
            working_bundles.append(cert_path)
            
        except Exception as e:
            print(f"  ❌ Failed: {str(e)[:100]}...")
    
    return working_bundles

def main():
    print("="*70)
    print("ADD CERTIFICATE TO ALL POSSIBLE LOCATIONS")
    print("="*70)
    print("This script adds the PaddleOCR certificate to all certificate bundles.")
    print()
    
    hostname = "paddle-model-ecology.bj.bcebos.com"
    
    # Get certificate
    cert_pem = get_server_certificate(hostname)
    if not cert_pem:
        print("❌ Could not retrieve certificate")
        sys.exit(1)
    
    print("✅ Certificate retrieved")
    print(f"Certificate preview:\n{cert_pem[:200]}...")
    
    # Get all certificate locations
    cert_locations = get_all_cert_locations()
    
    if not cert_locations:
        print("❌ No certificate locations found")
        sys.exit(1)
    
    print(f"\nFound {len(cert_locations)} certificate locations")
    
    # Ask for confirmation
    response = input(f"\nAdd certificate to all {len(cert_locations)} locations? (y/n): ").strip().lower()
    if response != 'y':
        print("Operation cancelled")
        sys.exit(0)
    
    # Add certificate to all locations
    success_count = 0
    for location in cert_locations:
        if add_cert_to_file(cert_pem, location, hostname):
            success_count += 1
    
    print(f"\n✅ Successfully added certificate to {success_count}/{len(cert_locations)} locations")
    
    # Test SSL connections
    working_bundles = test_ssl_with_each_bundle(hostname, cert_locations)
    
    if working_bundles:
        print(f"\n🎉 SUCCESS! SSL working with {len(working_bundles)} bundle(s):")
        for bundle in working_bundles:
            print(f"  ✅ {bundle}")
        
        print("\nNow test PaddleOCR:")
        print("python redact_multi_ocr.py input.pdf output.pdf targets.json")
    else:
        print("\n⚠️  Certificate added but SSL still not working.")
        print("PaddleOCR might be using a different certificate mechanism.")
        print("\nTry running with SSL bypass:")
        print("python redact_multi_ocr.py input.pdf output.pdf targets.json --ssl-bypass")

if __name__ == "__main__":
    main()
