import fitz  # PyMuPDF
import json
import sys
from PIL import Image, ImageDraw, ImageEnhance, ImageFilter
import pytesseract
import io
import cv2
import numpy as np
from difflib import SequenceMatcher

# Configure pytesseract to use the Tesseract installation
pytesseract.pytesseract.tesseract_cmd = r'C:\softies\tesseract\tesseract.exe'

def preprocess_image_for_ocr(image):
    """
    Apply the best preprocessing technique for OCR.
    """
    # Convert PIL to OpenCV format
    img_array = np.array(image)
    if len(img_array.shape) == 3:
        img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    else:
        gray = img_array

    # Apply OTSU thresholding (best performer from previous tests)
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)
    _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    return Image.fromarray(thresh)

def extract_all_text_from_page(page):
    """
    Run OCR once per page and cache all detected text with coordinates.
    """
    print(f"DEBUG: Running OCR on page...")

    # Convert page to high-resolution image
    pix = page.get_pixmap(dpi=400)
    img_data = pix.tobytes("png")
    image = Image.open(io.BytesIO(img_data))

    # Preprocess image
    processed_image = preprocess_image_for_ocr(image)

    # Run OCR with optimal configuration
    custom_config = r'--oem 3 --psm 6'

    try:
        ocr_data = pytesseract.image_to_data(processed_image, output_type=pytesseract.Output.DICT, config=custom_config)
    except Exception as e:
        print(f"DEBUG: OCR failed: {e}")
        return [], image

    # Extract all text elements with coordinates
    text_elements = []

    for i in range(len(ocr_data['text'])):
        detected_text = ocr_data['text'][i].strip()
        if not detected_text or len(detected_text) < 2:
            continue

        confidence = ocr_data['conf'][i]
        if confidence < 30:  # Skip very low confidence results
            continue

        text_element = {
            'text': detected_text,
            'confidence': confidence,
            'coordinates': (ocr_data['left'][i], ocr_data['top'][i],
                          ocr_data['width'][i], ocr_data['height'][i])
        }
        text_elements.append(text_element)

    print(f"DEBUG: Extracted {len(text_elements)} text elements from page")

    # Show sample of detected text
    sample_texts = [elem['text'] for elem in text_elements[:10]]
    print(f"DEBUG: Sample detected text: {sample_texts}")

    return text_elements, image

def find_matches_in_cached_text(text_elements, text_to_find):
    """
    Search for matches in the cached OCR results.
    """
    print(f"DEBUG: Searching cached text for: '{text_to_find}'")

    matches = []

    for element in text_elements:
        detected_text = element['text']
        match_score = 0
        match_type = ""

        # Method 1: Exact substring match
        if text_to_find.lower() in detected_text.lower():
            match_score = 1.0
            match_type = "exact substring"

        # Method 2: Fuzzy matching
        elif len(text_to_find) > 3:
            similarity = SequenceMatcher(None, text_to_find.lower(), detected_text.lower()).ratio()
            if similarity > 0.75:
                match_score = similarity
                match_type = f"fuzzy match ({similarity:.2f})"

        # Method 3: Character overlap for numbers/IDs
        elif len(text_to_find) > 4 and any(c.isdigit() for c in text_to_find):
            common_chars = set(text_to_find.lower()) & set(detected_text.lower())
            overlap_ratio = len(common_chars) / len(set(text_to_find.lower()))
            if overlap_ratio > 0.6:
                match_score = overlap_ratio
                match_type = f"character overlap ({overlap_ratio:.2f})"

        # Method 4: Partial word matching (for addresses, names)
        elif len(text_to_find) > 6:
            words_target = text_to_find.lower().split()
            words_detected = detected_text.lower().split()

            # Check if any significant words match
            common_words = set(words_target) & set(words_detected)
            if common_words and len(common_words) >= len(words_target) * 0.5:
                match_score = len(common_words) / len(words_target)
                match_type = f"word overlap ({match_score:.2f})"

        if match_score > 0:
            match_info = {
                'text_to_find': text_to_find,
                'detected_text': detected_text,
                'match_score': match_score,
                'match_type': match_type,
                'confidence': element['confidence'],
                'coordinates': element['coordinates']
            }
            matches.append(match_info)

    # Sort by match score and confidence
    matches.sort(key=lambda x: (x['match_score'], x['confidence']), reverse=True)

    print(f"DEBUG: Found {len(matches)} matches for '{text_to_find}'")
    for match in matches[:2]:  # Show top 2
        print(f"  '{match['detected_text']}' - {match['match_type']} (conf: {match['confidence']})")

    return matches

def apply_redactions_to_page(page, matches, original_image):
    """
    Apply redaction annotations to the page based on matches.
    """
    redactions_applied = 0

    for match in matches[:1]:  # Only apply the best match to avoid over-redaction
        x, y, w, h = match['coordinates']

        # Convert image coordinates to PDF coordinates
        img_width, img_height = original_image.size
        pdf_width, pdf_height = page.rect.width, page.rect.height

        x1 = (x / img_width) * pdf_width
        y1 = (y / img_height) * pdf_height
        x2 = ((x + w) / img_width) * pdf_width
        y2 = ((y + h) / img_height) * pdf_height

        # Add padding
        padding = 3
        x1 = max(0, x1 - padding)
        y1 = max(0, y1 - padding)
        x2 = min(pdf_width, x2 + padding)
        y2 = min(pdf_height, y2 + padding)

        # Create redaction annotation
        redact_rect = fitz.Rect(x1, y1, x2, y2)
        page.add_redact_annot(redact_rect)

        print(f"DEBUG: Applied redaction for '{match['detected_text']}' at ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")
        redactions_applied += 1

    return redactions_applied

def main():
    if len(sys.argv) != 4:
        print("Usage: python redact_optimized_ocr.py <input_pdf> <output_pdf> <redaction_json>")
        sys.exit(1)

    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    redaction_json = sys.argv[3]

    try:
        with open(redaction_json, "r") as f:
            redaction_data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"Error reading or parsing {redaction_json}: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"Error: {redaction_json} not found.")
        sys.exit(1)

    doc = fitz.open(input_pdf)

    # Group targets by page
    targets_by_page = {}
    for target in redaction_data:
        page_num = target.get("page", 1)
        if page_num not in targets_by_page:
            targets_by_page[page_num] = []
        targets_by_page[page_num].append(target)

    # Process each page
    for page_num in range(len(doc)):
        page = doc[page_num]
        page_targets = targets_by_page.get(page_num + 1, [])

        if not page_targets:
            continue

        print(f"\nProcessing page {page_num + 1}...")

        # Extract all text from page once (cache OCR results)
        text_elements, original_image = extract_all_text_from_page(page)

        # Process all targets for this page using cached OCR results
        for target in page_targets:
            if target["type"] in ["TEXT_EXACT", "TEXT_IN_IMAGE"]:
                content = target["content"]

                # Search in cached results
                matches = find_matches_in_cached_text(text_elements, content)

                # Apply redactions
                if matches:
                    apply_redactions_to_page(page, matches, original_image)
                else:
                    print(f"DEBUG: No matches found for '{content}'")

            elif target["type"] == "IMAGE_AREA":
                coords = target.get("bbox") or target.get("coordinates")
                if coords:
                    x1, y1, x2, y2 = coords
                    rect = fitz.Rect(x1, y1, x2, y2)
                    page.add_redact_annot(rect)
                    print(f"DEBUG: Image area redacted at ({x1}, {y1}, {x2}, {y2})")

    # Apply all redactions
    for page in doc:
        page.apply_redactions()

    # Save the document
    doc.save(output_pdf, garbage=4, deflate=True, clean=True)
    doc.close()

    print(f"\nOptimized OCR redaction complete. Output saved to: {output_pdf}")

if __name__ == "__main__":
    main()
