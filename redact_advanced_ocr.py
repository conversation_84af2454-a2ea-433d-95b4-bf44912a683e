import fitz  # PyMuPDF
import json
import sys
from PIL import Image, ImageDraw, ImageEnhance, ImageFilter, ImageOps
import pytesseract
import io
import cv2
import numpy as np
from difflib import SequenceMatcher

# Configure pytesseract to use the Tesseract installation
pytesseract.pytesseract.tesseract_cmd = r'C:\softies\tesseract\tesseract.exe'

def preprocess_image_for_ocr(image):
    """
    Advanced image preprocessing for better OCR accuracy.
    """
    # Convert PIL to OpenCV format
    img_array = np.array(image)
    if len(img_array.shape) == 3:
        img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
    else:
        img_cv = img_array

    # Convert to grayscale
    if len(img_cv.shape) == 3:
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    else:
        gray = img_cv

    # Apply multiple preprocessing techniques
    processed_images = []

    # 1. Original grayscale
    processed_images.append(('original', gray))

    # 2. Gaussian blur + threshold
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)
    _, thresh1 = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    processed_images.append(('otsu_threshold', thresh1))

    # 3. Adaptive threshold
    adaptive_thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    processed_images.append(('adaptive_threshold', adaptive_thresh))

    # 4. Morphological operations
    kernel = np.ones((2,2), np.uint8)
    morph = cv2.morphologyEx(thresh1, cv2.MORPH_CLOSE, kernel)
    processed_images.append(('morphological', morph))

    # 5. Contrast enhancement
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    processed_images.append(('enhanced_contrast', enhanced))

    return processed_images

def advanced_ocr_search(image, text_to_find):
    """
    Use multiple OCR configurations and image preprocessing techniques.
    """
    print(f"DEBUG: Advanced OCR search for: '{text_to_find}'")

    # Get multiple preprocessed versions of the image
    processed_images = preprocess_image_for_ocr(image)

    # Multiple OCR configurations
    ocr_configs = [
        r'--oem 3 --psm 6',   # Default
        r'--oem 3 --psm 8',   # Single word
        r'--oem 3 --psm 7',   # Single text line
        r'--oem 3 --psm 13',  # Raw line
        r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz /-:.,()#',
        r'--oem 1 --psm 6',   # LSTM engine
    ]

    all_results = []

    # Try each preprocessing method with each OCR config
    for img_name, processed_img in processed_images:
        # Convert back to PIL for pytesseract
        pil_img = Image.fromarray(processed_img)

        for config in ocr_configs:
            try:
                # Get OCR data
                ocr_data = pytesseract.image_to_data(pil_img, output_type=pytesseract.Output.DICT, config=config)

                # Store results with metadata
                result = {
                    'preprocessing': img_name,
                    'config': config,
                    'data': ocr_data,
                    'original_image': image  # Keep reference to original for coordinate mapping
                }
                all_results.append(result)

            except Exception as e:
                print(f"DEBUG: OCR failed for {img_name} with {config}: {e}")
                continue

    print(f"DEBUG: Generated {len(all_results)} OCR result sets")

    # Find matches across all results
    best_matches = []

    for result in all_results:
        ocr_data = result['data']

        for i in range(len(ocr_data['text'])):
            detected_text = ocr_data['text'][i].strip()
            if not detected_text or len(detected_text) < 2:
                continue

            confidence = ocr_data['conf'][i]
            if confidence < 30:  # Skip very low confidence results
                continue

            match_score = 0
            match_type = ""

            # Exact substring match
            if text_to_find.lower() in detected_text.lower():
                match_score = 1.0
                match_type = "exact substring"

            # Fuzzy matching
            elif len(text_to_find) > 3:
                similarity = SequenceMatcher(None, text_to_find.lower(), detected_text.lower()).ratio()
                if similarity > 0.75:  # Lower threshold for better recall
                    match_score = similarity
                    match_type = f"fuzzy match ({similarity:.2f})"

            # Character overlap for numbers/IDs
            elif len(text_to_find) > 4 and any(c.isdigit() for c in text_to_find):
                common_chars = set(text_to_find.lower()) & set(detected_text.lower())
                overlap_ratio = len(common_chars) / len(set(text_to_find.lower()))
                if overlap_ratio > 0.6:
                    match_score = overlap_ratio
                    match_type = f"character overlap ({overlap_ratio:.2f})"

            if match_score > 0:
                match_info = {
                    'text_to_find': text_to_find,
                    'detected_text': detected_text,
                    'match_score': match_score,
                    'match_type': match_type,
                    'confidence': confidence,
                    'coordinates': (ocr_data['left'][i], ocr_data['top'][i],
                                  ocr_data['width'][i], ocr_data['height'][i]),
                    'preprocessing': result['preprocessing'],
                    'config': result['config']
                }
                best_matches.append(match_info)

    # Sort by match score and confidence
    best_matches.sort(key=lambda x: (x['match_score'], x['confidence']), reverse=True)

    # Remove duplicates (same coordinates)
    unique_matches = []
    seen_coords = set()

    for match in best_matches:
        coord_key = match['coordinates']
        if coord_key not in seen_coords:
            unique_matches.append(match)
            seen_coords.add(coord_key)

    print(f"DEBUG: Found {len(unique_matches)} unique matches for '{text_to_find}'")
    for match in unique_matches[:3]:  # Show top 3
        print(f"  '{match['detected_text']}' - {match['match_type']} (conf: {match['confidence']}, prep: {match['preprocessing']})")

    return unique_matches

def redact_with_advanced_ocr(page, text_to_find):
    """
    Use advanced OCR techniques for better accuracy.
    """
    # Convert page to high-resolution image
    pix = page.get_pixmap(dpi=400)  # Higher DPI for better OCR
    img_data = pix.tobytes("png")
    image = Image.open(io.BytesIO(img_data))

    # Find matches using advanced OCR
    matches = advanced_ocr_search(image, text_to_find)

    matches_applied = 0

    for match in matches:
        x, y, w, h = match['coordinates']

        # Convert image coordinates to PDF coordinates
        img_width, img_height = image.size
        pdf_width, pdf_height = page.rect.width, page.rect.height

        x1 = (x / img_width) * pdf_width
        y1 = (y / img_height) * pdf_height
        x2 = ((x + w) / img_width) * pdf_width
        y2 = ((y + h) / img_height) * pdf_height

        # Add padding
        padding = 3
        x1 = max(0, x1 - padding)
        y1 = max(0, y1 - padding)
        x2 = min(pdf_width, x2 + padding)
        y2 = min(pdf_height, y2 + padding)

        # Create redaction annotation
        redact_rect = fitz.Rect(x1, y1, x2, y2)
        page.add_redact_annot(redact_rect)

        print(f"DEBUG: Applied redaction for '{match['detected_text']}' at ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")
        matches_applied += 1

        # Only apply the best match to avoid over-redaction
        break

    return matches_applied

def main():
    if len(sys.argv) != 4:
        print("Usage: python redact_advanced_ocr.py <input_pdf> <output_pdf> <redaction_json>")
        sys.exit(1)

    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    redaction_json = sys.argv[3]

    try:
        with open(redaction_json, "r") as f:
            redaction_data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"Error reading or parsing {redaction_json}: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"Error: {redaction_json} not found.")
        sys.exit(1)

    doc = fitz.open(input_pdf)

    # Group targets by page
    targets_by_page = {}
    for target in redaction_data:
        page_num = target.get("page", 1)
        if page_num not in targets_by_page:
            targets_by_page[page_num] = []
        targets_by_page[page_num].append(target)

    # Process each page
    for page_num in range(len(doc)):
        page = doc[page_num]
        page_targets = targets_by_page.get(page_num + 1, [])

        if not page_targets:
            continue

        print(f"\nProcessing page {page_num + 1}...")

        for target in page_targets:
            if target["type"] in ["TEXT_EXACT", "TEXT_IN_IMAGE"]:
                content = target["content"]
                matches = redact_with_advanced_ocr(page, content)

            elif target["type"] == "IMAGE_AREA":
                coords = target.get("bbox") or target.get("coordinates")
                if coords:
                    x1, y1, x2, y2 = coords
                    rect = fitz.Rect(x1, y1, x2, y2)
                    page.add_redact_annot(rect)
                    print(f"DEBUG: Image area redacted at ({x1}, {y1}, {x2}, {y2})")

    # Apply all redactions
    for page in doc:
        page.apply_redactions()

    # Save the document
    doc.save(output_pdf, garbage=4, deflate=True, clean=True)
    doc.close()

    print(f"\nAdvanced OCR redaction complete. Output saved to: {output_pdf}")

if __name__ == "__main__":
    main()
